#!/usr/bin/env python3
"""
Test script to verify Huawei provider can be imported and instantiated.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_huawei_provider_import():
    """Test importing Huawei provider classes"""
    try:
        from api.providers.huawei_provider import (
            HuaweiAccountProvider,
            HuaweiMetricsProvider,
            HuaweiConsoleProvider,
            HuaweiS3Provider,
            HuaweiIAMProvider,
        )
        print("✅ Successfully imported all Huawei provider classes")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Huawei provider classes: {e}")
        return False

def test_huawei_provider_instantiation():
    """Test instantiating Huawei provider classes"""
    try:
        from api.providers.huawei_provider import HuaweiAccountProvider
        
        # Mock configuration
        config = {
            "obs_endpoint_url": "https://obs.region.myhuaweicloud.com",
            "obs_access_key": "test_access_key",
            "obs_secret_key": "test_secret_key",
            "cloud_eye_endpoint_url": "https://ces.region.myhuaweicloud.com",
            "huawei_console_endpoint": "https://console.huaweicloud.com",
            "console_username": "test_user",
            "console_password": "test_pass",
        }
        
        # Try to instantiate
        provider = HuaweiAccountProvider("test-region", config)
        print("✅ Successfully instantiated HuaweiAccountProvider")
        return True
    except Exception as e:
        print(f"❌ Failed to instantiate Huawei provider: {e}")
        return False

def test_factory_integration():
    """Test Huawei provider integration with factory"""
    try:
        from api.providers.factory import ProviderFactory
        
        # Check if Huawei provider is registered
        if "huawei" in ProviderFactory._providers:
            print("✅ Huawei provider is registered in factory")
            return True
        else:
            print("❌ Huawei provider is not registered in factory")
            return False
    except Exception as e:
        print(f"❌ Failed to test factory integration: {e}")
        return False

if __name__ == "__main__":
    print("Testing Huawei provider integration...")
    print("=" * 50)
    
    tests = [
        test_huawei_provider_import,
        test_huawei_provider_instantiation,
        test_factory_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Huawei provider integration is working.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        sys.exit(1)
