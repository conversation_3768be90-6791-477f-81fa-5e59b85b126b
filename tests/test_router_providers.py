"""
Unit tests for router integration with provider abstraction layer.

This test suite validates that the routers correctly use the provider factory
and that the dependency injection works as expected.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Import the routers and dependencies
from api.routers.vault import router as vault_router
from api.routers.metrics import router as metrics_router
from api.routers.console import router as console_router
from api.providers.factory import ProviderFactory
from api.config import Regions


class TestVaultRouterProviderIntegration:
    """Test vault router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(vault_router)
        self.client = TestClient(self.app)

    @patch('api.routers.vault.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_get_account_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetAccount endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_provider
        mock_provider.get_account = AsyncMock(return_value=[{"name": "test-account", "id": "123"}])

        # Make request
        response = self.client.get(
            "/vault/account/test-account",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_account_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.get_account.assert_called_once_with("test-account")

    @patch('api.routers.vault.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_search_accounts_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that SearchAccounts endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_provider
        mock_provider.search_accounts = AsyncMock(return_value=[{"name": "account1"}])

        # Make request
        response = self.client.get(
            "/vault/accounts/search",
            params={"region": "paris-1", "query": "test", "starts_with": True, "ends_with": False},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_account_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.search_accounts.assert_called_once_with("test", True, False)

    @patch('api.routers.vault.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_get_account_buckets_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetAccountBuckets endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_provider
        mock_provider.get_account_buckets = AsyncMock(return_value=["bucket1", "bucket2"])

        # Make request
        response = self.client.get(
            "/vault/account/test-account/buckets",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_account_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.get_account_buckets.assert_called_once_with("test-account")


class TestMetricsRouterProviderIntegration:
    """Test metrics router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(metrics_router)
        self.client = TestClient(self.app)

    @patch('api.routers.metrics.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_get_metrics_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetMetrics endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_metrics_provider.return_value = mock_provider
        mock_provider.get_metrics = AsyncMock(return_value={"objects": 100, "bytes": 1024})

        # Make request
        response = self.client.get(
            "/metrics/test-account/bucket",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_metrics_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.get_metrics.assert_called_once_with("test-account", "bucket")

    @patch('api.routers.metrics.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_get_account_metrics_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetAccountMetrics endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_metrics_provider.return_value = mock_provider
        mock_provider.get_account_metrics = AsyncMock(return_value={"total_objects": 500})

        # Make request
        response = self.client.get(
            "/metrics/test-account",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_metrics_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.get_account_metrics.assert_called_once_with("test-account")


class TestConsoleRouterProviderIntegration:
    """Test console router integration with providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.app = FastAPI()
        self.app.include_router(console_router)
        self.client = TestClient(self.app)

    @patch('api.routers.console.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_get_console_account_uses_provider_factory(self, mock_auth, mock_provider_factory):
        """Test that GetConsoleAccount endpoint uses ProviderFactory."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        mock_provider = Mock()
        mock_factory.get_console_provider.return_value = mock_provider
        mock_provider.get_account = AsyncMock(return_value={"id": "123", "name": "test-account"})

        # Make request
        response = self.client.get(
            "/console/account/test-account",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify provider factory was used
        mock_factory.get_console_provider.assert_called_once_with(Regions.PARIS_1)
        mock_provider.get_account.assert_called_once_with("test-account")

    @patch('api.routers.console.ProviderFactory')
    @patch('api.auth.get_current_user')
    def test_create_console_account_uses_multiple_providers(self, mock_auth, mock_provider_factory):
        """Test that CreateConsoleAccount endpoint uses multiple providers."""
        # Setup mocks
        mock_auth.return_value = {"sub": "test-user"}
        
        mock_factory = Mock()
        mock_provider_factory.return_value = mock_factory
        
        # Mock account provider (to check if account exists)
        mock_account_provider = Mock()
        mock_factory.get_account_provider.return_value = mock_account_provider
        mock_account_provider.get_account = AsyncMock(return_value=None)  # Account doesn't exist
        
        # Mock console provider (to create account)
        mock_console_provider = Mock()
        mock_factory.get_console_provider.return_value = mock_console_provider
        mock_console_provider.create_account = AsyncMock(return_value={"id": "456", "name": "new-account"})

        # Make request
        response = self.client.post(
            "/console/account/new-account",
            params={"region": "paris-1"},
            headers={"Authorization": "Bearer fake-token"}
        )

        # Verify both providers were used
        mock_factory.get_account_provider.assert_called_once_with(Regions.PARIS_1)
        mock_factory.get_console_provider.assert_called_once_with(Regions.PARIS_1)
        mock_account_provider.get_account.assert_called_once_with("new-account")
        mock_console_provider.create_account.assert_called_once_with("new-account")


class TestProviderDependencyInjection:
    """Test that provider dependency injection works correctly."""

    @patch('api.providers.factory.ProviderFactory')
    def test_provider_factory_singleton_behavior(self, mock_provider_factory_class):
        """Test that ProviderFactory behaves as expected in dependency injection."""
        # Import the dependency functions
        from api.routers.vault import get_account_provider
        from api.routers.metrics import get_metrics_provider
        from api.routers.console import get_console_provider, get_account_provider as console_get_account_provider

        # Setup mock
        mock_factory_instance = Mock()
        mock_provider_factory_class.return_value = mock_factory_instance
        
        mock_account_provider = Mock()
        mock_metrics_provider = Mock()
        mock_console_provider = Mock()
        
        mock_factory_instance.get_account_provider.return_value = mock_account_provider
        mock_factory_instance.get_metrics_provider.return_value = mock_metrics_provider
        mock_factory_instance.get_console_provider.return_value = mock_console_provider

        # Test dependency functions
        account_provider = get_account_provider(Regions.PARIS_1)
        metrics_provider = get_metrics_provider(Regions.PARIS_1)
        console_provider = get_console_provider(Regions.PARIS_1)
        console_account_provider = console_get_account_provider(Regions.PARIS_1)

        # Verify correct providers are returned
        assert account_provider == mock_account_provider
        assert metrics_provider == mock_metrics_provider
        assert console_provider == mock_console_provider
        assert console_account_provider == mock_account_provider

        # Verify factory methods were called with correct regions
        assert mock_factory_instance.get_account_provider.call_count == 2  # Called twice
        assert mock_factory_instance.get_metrics_provider.call_count == 1
        assert mock_factory_instance.get_console_provider.call_count == 1


if __name__ == "__main__":
    pytest.main([__file__])
