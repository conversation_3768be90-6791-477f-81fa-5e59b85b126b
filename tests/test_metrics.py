import os
import time
import unittest
from datetime import datetime, timedelta

import requests
from dotenv import load_dotenv
from requests.adapters import HTTPAdapter  # type: ignore
from requests.packages.urllib3.util.retry import Retry  # type: ignore

load_dotenv()

retry_strategy = Retry(
    total=3,  # Total number of retries (including the initial request)
    status_forcelist=[500, 423],  # HTTP status codes to retry
    backoff_factor=0.5,  # Factor by which the delay increases after each retry
    method_whitelist=["GET", "POST"],  # HTTP methods to retry
)

adapter = HTTPAdapter(max_retries=retry_strategy)
client: requests.sessions.Session = requests.Session()
client.mount("http://", adapter)
client.mount("https://", adapter)


def get_token(client_id: str, client_secret: str):
    endpoint = os.getenv("IDENTITY_PROVIDER_TOKEN_URL", "")
    headers = {
        "accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }
    try:
        r = client.post(
            endpoint,
            headers=headers,
            data=payload,
        )
        r.raise_for_status()
    except Exception as e:
        raise e

    return r.json()["access_token"]


class TestMetrics(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.url = "http://localhost:8071"
        cls.account_id = os.environ.get("ACCOUNT_ID", None)
        assert cls.account_id is not None, "ACCOUNT_ID not set"
        cls.user = os.environ.get("TEST_USER", None)
        assert cls.user is not None, "TEST_USER not set"
        cls.bucket = os.environ.get("TEST_BUCKET", None)
        assert cls.bucket is not None, "TEST_BUCKET not set"
        cls.region = os.environ.get("REGION", None)
        assert cls.region is not None, "REGION not set"

        cls.clientid = os.environ.get("CLIENT_ID_JSO", None)
        assert cls.clientid is not None, "CLIENT_ID_JSO not set"
        cls.clientsecret = os.environ.get("CLIENT_SECRET_JSO", None)
        assert cls.clientsecret is not None, "CLIENT_SECRET_JSO not set"
        cls.token = get_token(cls.clientid, cls.clientsecret)
        assert cls.token is not None, "Failed to get token"

    def _make_request(self, endpoint: str, params: dict):
        """Helper method to make authenticated requests"""
        headers = {"Authorization": f"Bearer {self.token}"}
        try:
            r = client.get(endpoint, headers=headers, params=params)
            r.raise_for_status()
            return r
        except Exception as e:
            raise e

    def _validate_metrics_response(self, response_data: dict, expected_type: str):
        """Helper method to validate metrics response structure"""
        # Check top-level structure
        self.assertIn("metrics", response_data)
        self.assertIsInstance(response_data["metrics"], list)
        
        # Check type-specific fields
        if expected_type == "account":
            self.assertIn("accountId", response_data)
            self.assertEqual(response_data["accountId"], self.account_id)
        elif expected_type == "user":
            self.assertIn("userId", response_data)
            self.assertEqual(response_data["userId"], self.user)
        elif expected_type == "bucket":
            self.assertIn("bucketName", response_data)
            self.assertEqual(response_data["bucketName"], self.bucket)

        # Validate metrics structure if present
        if response_data["metrics"]:
            for metric in response_data["metrics"]:
                self.assertIn("interval", metric)
                self.assertIn("startDate", metric)
                self.assertIn("endDate", metric)
                self.assertIn("storageUtilized", metric)
                self.assertIn("numberOfObjects", metric)
                self.assertIn("numberOfOperations", metric)
                self.assertIsInstance(metric["numberOfOperations"], dict)

    # Account Metrics Tests
    def test_account_metrics_current(self):
        """Test account metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "current": True}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_week(self):
        """Test account metrics with week timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "week": True, "current": False}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_month(self):
        """Test account metrics with month timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "month": True, "current": False}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_year(self):
        """Test account metrics with year timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "year": True, "current": False}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_custom(self):
        """Test account metrics with custom timeframe"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        
        # Custom timeframe: last 24 hours
        end_time = int(datetime.now().timestamp())
        start_time = int((datetime.now() - timedelta(days=1)).timestamp())
        
        params = {
            "region": self.region,
            "custom": True,
            "current": False,
            "start_time": start_time,
            "end_time": end_time
        }
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_human_readable(self):
        """Test account metrics with human readable format"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "current": True, "human": True}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "account")

    def test_account_metrics_multiple_timeframes(self):
        """Test account metrics with multiple timeframes"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {
            "region": self.region,
            "current": True,
            "week": True,
            "month": True
        }
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        response_data = r.json()
        self._validate_metrics_response(response_data, "account")
        
        # Should have metrics for multiple intervals
        intervals = [metric["interval"] for metric in response_data["metrics"]]
        self.assertIn("current", intervals)
        self.assertIn("week", intervals)
        self.assertIn("month", intervals)

    def test_account_metrics_no_timeframe_error(self):
        """Test account metrics with no timeframe selected returns error"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {
            "region": self.region,
            "current": False,
            "week": False,
            "month": False,
            "year": False,
            "custom": False
        }
        
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.get(endpoint, headers=headers, params=params)
        self.assertEqual(r.status_code, 400)
        self.assertIn("At least one timeframe must be selected", r.json()["message"])

    # User Metrics Tests
    def test_user_metrics_current(self):
        """Test user metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/user/{self.user}"
        params = {"region": self.region, "current": True}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "user")

    def test_user_metrics_custom(self):
        """Test user metrics with custom timeframe"""
        endpoint = f"{self.url}/metrics/user/{self.user}"
        
        # Custom timeframe: last 24 hours
        end_time = int(datetime.now().timestamp())
        start_time = int((datetime.now() - timedelta(days=1)).timestamp())
        
        params = {
            "region": self.region,
            "custom": True,
            "current": False,
            "start_time": start_time,
            "end_time": end_time
        }
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "user")

    # Bucket Metrics Tests
    def test_bucket_metrics_current(self):
        """Test bucket metrics with current timeframe"""
        endpoint = f"{self.url}/metrics/bucket/{self.bucket}"
        params = {"region": self.region, "current": True}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "bucket")

    def test_bucket_metrics_week(self):
        """Test bucket metrics with week timeframe"""
        endpoint = f"{self.url}/metrics/bucket/{self.bucket}"
        params = {"region": self.region, "week": True, "current": False}
        
        r = self._make_request(endpoint, params)
        self.assertEqual(r.status_code, 200)
        self._validate_metrics_response(r.json(), "bucket")

    # Error Handling Tests
    def test_account_metrics_invalid_account(self):
        """Test account metrics with invalid account ID"""
        endpoint = f"{self.url}/metrics/account/invalid-account-id"
        params = {"region": self.region, "current": True}
        
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.get(endpoint, headers=headers, params=params)
        self.assertEqual(r.status_code, 404)

    def test_user_metrics_invalid_user(self):
        """Test user metrics with invalid user"""
        endpoint = f"{self.url}/metrics/user/invalid-user"
        params = {"region": self.region, "current": True}
        
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.get(endpoint, headers=headers, params=params)
        self.assertEqual(r.status_code, 404)

    def test_bucket_metrics_invalid_bucket(self):
        """Test bucket metrics with invalid bucket"""
        endpoint = f"{self.url}/metrics/bucket/invalid-bucket"
        params = {"region": self.region, "current": True}
        
        headers = {"Authorization": f"Bearer {self.token}"}
        r = client.get(endpoint, headers=headers, params=params)
        self.assertEqual(r.status_code, 404)

    # Performance Tests
    def test_metrics_response_time(self):
        """Test that metrics endpoints respond within reasonable time"""
        endpoint = f"{self.url}/metrics/account/{self.account_id}"
        params = {"region": self.region, "current": True}
        
        start_time = time.time()
        r = self._make_request(endpoint, params)
        response_time = time.time() - start_time
        
        self.assertEqual(r.status_code, 200)
        self.assertLess(response_time, 10.0, "Metrics response took too long")


if __name__ == "__main__":
    unittest.main()
