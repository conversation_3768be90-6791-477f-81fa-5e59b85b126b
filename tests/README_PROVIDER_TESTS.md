# Provider Abstraction Unit Test Suite

This directory contains comprehensive unit tests for the provider abstraction layer implemented in the JSO API. These tests validate the multi-provider architecture without requiring external services or conflicting with existing integration tests.

## Test Structure

### Test Files

1. **`test_providers.py`** - Core provider unit tests
   - Tests for `ProviderFactory` class
   - Tests for all Scality provider implementations
   - Integration tests for provider workflows
   - Error handling tests

2. **`test_router_providers.py`** - Router integration tests
   - Tests for router dependency injection
   - Tests for provider factory usage in endpoints
   - Tests for multi-provider workflows in routers

3. **`run_provider_tests.py`** - Test runner script
   - Runs provider tests independently
   - Supports running specific test classes
   - Provides clear output and status reporting

### Test Categories

#### Unit Tests (`test_providers.py`)

- **`TestProviderFactory`**: Tests the factory pattern implementation
- **`TestScalityAccountProvider`**: Tests account provider functionality
- **`TestScalityMetricsProvider`**: Tests metrics provider functionality  
- **`TestScalityConsoleProvider`**: Tests console provider functionality
- **`TestScalityS3Provider`**: Tests S3 provider functionality
- **`TestScalityIAMProvider`**: Tests IAM provider functionality
- **`TestProviderIntegration`**: Tests provider interaction workflows
- **`TestProviderErrorHandling`**: Tests error handling and exception propagation

#### Router Integration Tests (`test_router_providers.py`)

- **`TestVaultRouterProviderIntegration`**: Tests vault router provider usage
- **`TestMetricsRouterProviderIntegration`**: Tests metrics router provider usage
- **`TestConsoleRouterProviderIntegration`**: Tests console router provider usage
- **`TestProviderDependencyInjection`**: Tests FastAPI dependency injection

## Running Tests

### Prerequisites

Install test dependencies:
```bash
# Using uv (recommended)
uv sync

# Or using pip
pip install pytest pytest-asyncio pytest-mock
```

### Running All Provider Tests

```bash
# Using the test runner script
python tests/run_provider_tests.py

# Or using pytest directly
pytest tests/test_providers.py tests/test_router_providers.py -v
```

### Running Specific Test Classes

```bash
# Run only factory tests
python tests/run_provider_tests.py TestProviderFactory

# Run only account provider tests
python tests/run_provider_tests.py TestScalityAccountProvider

# Run only router integration tests
python tests/run_provider_tests.py TestVaultRouterProviderIntegration
```

### Running Individual Test Methods

```bash
# Run a specific test method
pytest tests/test_providers.py::TestProviderFactory::test_get_account_provider_returns_scality_provider -v

# Run tests matching a pattern
pytest -k "test_get_account" tests/test_providers.py -v
```

## Test Coverage

The test suite covers:

### Provider Factory
- ✅ Correct provider instantiation for each type
- ✅ Region-specific provider creation
- ✅ Provider caching behavior
- ✅ Different regions return different instances

### Account Provider
- ✅ `get_account()` method calls
- ✅ `search_accounts()` method calls  
- ✅ `get_account_buckets()` method calls
- ✅ `get_account_credentials()` method calls
- ✅ VaultClient integration

### Metrics Provider
- ✅ `get_metrics()` method calls
- ✅ `get_account_metrics()` method calls
- ✅ Utapi integration

### Console Provider
- ✅ `get_account()` method calls
- ✅ `create_account()` method calls
- ✅ `delete_account_user()` method calls
- ✅ Console class integration

### S3 Provider
- ✅ `get_client()` boto3 client creation
- ✅ `get_resource()` boto3 resource creation
- ✅ Proper configuration and credentials handling

### IAM Provider
- ✅ `get_client()` IAM client creation
- ✅ `cleanup_account_iam()` method calls
- ✅ Ordered IAM cleanup workflow

### Router Integration
- ✅ Vault router endpoints use providers correctly
- ✅ Metrics router endpoints use providers correctly
- ✅ Console router endpoints use providers correctly
- ✅ Multi-provider workflows in complex endpoints
- ✅ FastAPI dependency injection works correctly

### Error Handling
- ✅ Provider exceptions are properly propagated
- ✅ Service-specific errors are handled correctly
- ✅ Connection errors are handled appropriately

## Test Design Principles

### Isolation
- Tests use mocking to avoid external dependencies
- No real API calls or service connections
- Each test is independent and can run in isolation

### Comprehensive Coverage
- All provider methods are tested
- All router integrations are validated
- Both success and error scenarios are covered

### Non-Interference
- Tests don't conflict with existing integration tests
- Separate test files and configuration
- Independent test runner

### Maintainability
- Clear test structure and naming
- Comprehensive documentation
- Easy to extend for new providers

## Adding New Tests

### For New Provider Methods

1. Add test method to appropriate provider test class
2. Use mocking to isolate the method under test
3. Verify both the method call and return value
4. Add error handling test if applicable

Example:
```python
@patch('api.providers.scality_provider.VaultClient')
@pytest.mark.asyncio
async def test_new_method_calls_vault_client(self, mock_vault_client):
    """Test that new_method calls VaultClient.new_method."""
    # Setup mock
    mock_client = Mock()
    mock_vault_client.return_value = mock_client
    mock_client.new_method = AsyncMock(return_value={"result": "success"})

    # Call method
    result = await self.provider.new_method("param")

    # Verify
    mock_vault_client.assert_called_once_with(self.region)
    mock_client.new_method.assert_called_once_with("param")
    assert result == {"result": "success"}
```

### For New Router Endpoints

1. Add test method to appropriate router test class
2. Mock the provider factory and providers
3. Make HTTP request using TestClient
4. Verify provider factory usage and method calls

### For New Providers

1. Create new test class following existing patterns
2. Test all provider methods with mocking
3. Add integration tests for provider workflows
4. Update test runner to include new tests

## Continuous Integration

These tests are designed to run in CI/CD pipelines:

- Fast execution (no external dependencies)
- Clear pass/fail status
- Detailed error reporting
- Compatible with standard pytest runners

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and PYTHONPATH includes the project root
2. **Mock Issues**: Verify mock patches target the correct import paths
3. **Async Test Issues**: Ensure `pytest-asyncio` is installed and `@pytest.mark.asyncio` is used

### Debug Mode

Run tests with more verbose output:
```bash
pytest tests/test_providers.py -v -s --tb=long
```

### Test Specific Functionality

Use pytest's filtering to focus on specific areas:
```bash
# Test only factory functionality
pytest -k "Factory" tests/test_providers.py

# Test only error handling
pytest -k "error" tests/test_providers.py

# Test only async methods
pytest -k "async" tests/test_providers.py
```
