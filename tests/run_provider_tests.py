#!/usr/bin/env python3
"""
Test runner for provider abstraction unit tests.

This script runs only the provider abstraction tests without interfering
with existing integration tests.
"""

import sys
import subprocess
from pathlib import Path


def run_provider_tests():
    """Run the provider abstraction unit tests."""
    test_dir = Path(__file__).parent
    
    # Test files to run
    test_files = [
        "test_providers.py",
        "test_router_providers.py"
    ]
    
    print("🧪 Running Provider Abstraction Unit Tests")
    print("=" * 50)
    
    all_passed = True
    
    for test_file in test_files:
        test_path = test_dir / test_file
        if not test_path.exists():
            print(f"❌ Test file not found: {test_file}")
            all_passed = False
            continue
            
        print(f"\n📋 Running {test_file}...")
        print("-" * 30)
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest", 
                str(test_path),
                "-v",
                "--tb=short",
                "--disable-warnings"
            ], cwd=test_dir.parent, capture_output=False)
            
            if result.returncode != 0:
                print(f"❌ Tests failed in {test_file}")
                all_passed = False
            else:
                print(f"✅ All tests passed in {test_file}")
                
        except Exception as e:
            print(f"❌ Error running {test_file}: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All provider abstraction tests passed!")
        return 0
    else:
        print("💥 Some tests failed!")
        return 1


def run_specific_test_class(test_class: str):
    """Run a specific test class."""
    test_dir = Path(__file__).parent
    
    print(f"🧪 Running specific test class: {test_class}")
    print("=" * 50)
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            f"-k", test_class,
            "test_providers.py",
            "test_router_providers.py",
            "-v",
            "--tb=short",
            "--disable-warnings"
        ], cwd=test_dir.parent)
        
        return result.returncode
        
    except Exception as e:
        print(f"❌ Error running test class {test_class}: {e}")
        return 1


def main():
    """Main entry point."""
    if len(sys.argv) > 1:
        # Run specific test class
        test_class = sys.argv[1]
        return run_specific_test_class(test_class)
    else:
        # Run all provider tests
        return run_provider_tests()


if __name__ == "__main__":
    sys.exit(main())
