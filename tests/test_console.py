import os
import time
import unittest

import requests
from dotenv import load_dotenv
from requests.adapters import HTTPAdapter  # type: ignore
from requests.packages.urllib3.util.retry import Retry  # type: ignore

load_dotenv()

retry_strategy = Retry(
    total=3,  # Total number of retries (including the initial request)
    status_forcelist=[500, 423],  # HTTP status codes to retry
    backoff_factor=0.5,  # Factor by which the delay increases after each retry
    method_whitelist=["GET", "POST"],  # HTTP methods to retry
)

adapter = HTTPAdapter(max_retries=retry_strategy)
client: requests.sessions.Session = requests.Session()
client.mount("http://", adapter)
client.mount("https://", adapter)


def get_token(client_id: str, client_secret: str):
    endpoint = os.getenv("IDENTITY_PROVIDER_TOKEN_URL", "")
    headers = {
        "accept": "application/json",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    payload = {
        "grant_type": "client_credentials",
        "client_id": client_id,
        "client_secret": client_secret,
    }
    try:
        r = client.post(
            endpoint,
            headers=headers,
            data=payload,
        )
        r.raise_for_status()
    except Exception as e:
        raise e

    return r.json()["access_token"]


class TestConsole(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.url = "http://localhost:8071"
        cls.account = os.environ.get("ACCOUNT", None)
        assert cls.account is not None, "ACCOUNT not set"
        cls.account_id = os.environ.get("ACCOUNT_ID", None)
        assert cls.account_id is not None, "ACCOUNT_ID not set"
        cls.region = os.environ.get("REGION", None)
        assert cls.region is not None, "REGION not set"

        cls.clientid = os.environ.get("CLIENT_ID_JSO", None)
        assert cls.clientid is not None, "CLIENT_ID_JSO not set"
        cls.clientsecret = os.environ.get("CLIENT_SECRET_JSO", None)
        assert cls.clientsecret is not None, "CLIENT_SECRET_JSO not set"
        cls.token = get_token(cls.clientid, cls.clientsecret)
        assert cls.token is not None, "Failed to get token"

    def test_get_account(self):
        endpoint = f"{self.url}/vault/account/{self.account}"
        headers = {"Authorization": f"Bearer {self.token}"}
        params = {"region": self.region, "starts_with": False, "ends_with": False}
        try:
            r = client.get(
                endpoint,
                headers=headers,
                params=params,
            )
            r.raise_for_status()
            self.assertEqual(r.status_code, 200)
            self.assertEqual(r.json()[0]["name"], self.account)
            self.assertEqual(r.json()[0]["id"], self.account_id)
        except Exception as e:
            raise e

    def test_get_account_cached(self):
        # First call to populate cache
        self.test_get_account()
        start_time = time.time()
        # Second call should use cache and be faster
        self.test_get_account()
        current_time = time.time()
        # Verify it was faster (cached)
        self.assertLess(current_time - start_time, 1)

    @unittest.skip("Not implemented")
    def test_create_account(self):
        pass

    @unittest.skip("Not implemented")
    def test_delete_account(self):
        pass
