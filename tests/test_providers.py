"""
Unit tests for the provider abstraction layer.

This test suite validates the provider factory pattern and provider implementations
without requiring external services or API endpoints.
"""

from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest

from api.config import Regions
from api.providers.base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
)

# Import the provider classes
from api.providers.factory import ProviderFactory
from api.providers.scality_provider import (
    Instance,
    ScalityAccountProvider,
    ScalityConsoleProvider,
    ScalityIAMProvider,
    ScalityMetricsProvider,
    ScalityS3Provider,
)


class TestProviderFactory:
    """Test the ProviderFactory class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.factory = ProviderFactory()

    def test_get_account_provider_returns_scality_provider(self):
        """Test that get_account_provider returns ScalityAccountProvider."""
        provider = self.factory.get_account_provider(Regions.fr_par)
        assert isinstance(provider, ScalityAccountProvider)

    def test_get_metrics_provider_returns_scality_provider(self):
        """Test that get_metrics_provider returns ScalityMetricsProvider."""
        provider = self.factory.get_metrics_provider(Regions.fr_par)
        assert isinstance(provider, ScalityMetricsProvider)

    def test_get_console_provider_returns_scality_provider(self):
        """Test that get_console_provider returns ScalityConsoleProvider."""
        provider = self.factory.get_console_provider(Regions.fr_par)
        assert isinstance(provider, ScalityConsoleProvider)

    def test_get_s3_provider_returns_scality_provider(self):
        """Test that get_s3_provider returns ScalityS3Provider."""
        provider = self.factory.get_s3_provider(Regions.fr_par)
        assert isinstance(provider, ScalityS3Provider)

    def test_get_iam_provider_returns_scality_provider(self):
        """Test that get_iam_provider returns ScalityIAMProvider."""
        provider = self.factory.get_iam_provider(
            Regions.fr_par,
            "http://localhost:8000",
            "test-access-key",
            "test-secret-key",
        )
        assert isinstance(provider, ScalityIAMProvider)

    def test_different_regions_return_different_instances(self):
        """Test that different regions return different provider instances."""
        provider1 = self.factory.get_account_provider(Regions.fr_par)
        provider2 = self.factory.get_account_provider(Regions.fr_lyo)
        assert provider1 is not provider2

    def test_same_region_returns_same_instance(self):
        """Test that same region returns the same provider instance (caching)."""
        provider1 = self.factory.get_account_provider(Regions.fr_par)
        provider2 = self.factory.get_account_provider(Regions.fr_par)
        assert provider1 is provider2


class TestScalityAccountProvider:
    """Test the ScalityAccountProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.config = {
            "vault_host": "localhost",
            "vault_port": "8500",
            "vault_access_key": "test-access-key",
            "vault_secret_key": "test-secret-key",
            "s3_endpoint_url": "http://localhost:8000",
        }
        # Mock VaultClient at class level to prevent actual instantiation
        with patch("api.providers.scality_provider.VaultClient") as mock_vault_client:
            self.mock_vault_client = mock_vault_client
            self.mock_vault_instance = Mock()
            mock_vault_client.return_value = self.mock_vault_instance
            self.provider = ScalityAccountProvider(self.region, self.config)

    @pytest.mark.asyncio
    async def test_get_account_calls_vault_client(self):
        """Test that get_account calls VaultClient.list_accounts."""
        # Setup mock return value
        self.mock_vault_instance.list_accounts = Mock(
            return_value={"accounts": [{"name": "test-account"}], "isTruncated": False}
        )

        # Call method
        result = await self.provider.get_account("test-account")

        # Verify
        self.mock_vault_client.assert_called_once_with(
            host="localhost",
            port=8500,
            use_https=True,
            access_key="test-access-key",
            secret_access_key="test-secret-key",
        )
        self.mock_vault_instance.list_accounts.assert_called_once_with(marker="")
        assert result == [{"name": "test-account"}]

    @pytest.mark.asyncio
    async def test_generate_account_access_key_calls_vault_client(self):
        """Test that generate_account_access_key calls VaultClient.generate_account_access_key."""
        # Setup mock return value
        expected_creds = {"access_key": "key", "secret_key": "secret"}
        self.mock_vault_instance.generate_account_access_key = Mock(
            return_value=expected_creds
        )

        # Call method
        result = await self.provider.generate_account_access_key("test-account")

        # Verify
        self.mock_vault_client.assert_called_once_with(
            host="localhost",
            port=8500,
            use_https=True,
            access_key="test-access-key",
            secret_access_key="test-secret-key",
        )
        self.mock_vault_instance.generate_account_access_key.assert_called_once_with(
            "test-account"
        )
        assert result == expected_creds


class TestScalityMetricsProvider:
    """Test the ScalityMetricsProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.config = {"s3_endpoint_url": "http://localhost:8000"}
        # Mock Utapi at class level to prevent actual instantiation
        with patch("api.providers.scality_provider.Utapi") as mock_utapi:
            self.mock_utapi = mock_utapi
            self.mock_utapi_instance = Mock()
            mock_utapi.return_value = self.mock_utapi_instance
            self.provider = ScalityMetricsProvider(self.region, self.config)

    @pytest.mark.asyncio
    async def test_get_metrics_calls_utapi(self):
        """Test that get_metrics calls Utapi.get_metrics."""
        # Setup mock
        expected_metrics = {"objects": 100}
        self.mock_utapi_instance.get_metrics = AsyncMock(return_value=expected_metrics)

        # Call method with correct signature
        result = await self.provider.get_metrics(Instance.ACCOUNT, "test-account", True)

        # Verify
        self.mock_utapi.assert_called_once_with(self.region)
        self.mock_utapi_instance.get_metrics.assert_called_once()
        assert result == expected_metrics


class TestScalityConsoleProvider:
    """Test the ScalityConsoleProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.config = {
            "s3_endpoint_url": "http://localhost:8000",
            "console_username": "admin",
            "console_password": "password",
        }
        # Mock Console at class level to prevent actual instantiation
        with patch("api.providers.scality_provider.Console") as mock_console:
            self.mock_console = mock_console
            self.mock_console_instance = Mock()
            mock_console.return_value = self.mock_console_instance
            self.provider = ScalityConsoleProvider(self.region, self.config)

    @pytest.mark.asyncio
    async def test_get_account_calls_console(self):
        """Test that get_account calls Console.get_account."""
        # Setup mock
        self.mock_console_instance.authenticate = AsyncMock()
        self.mock_console_instance.get_account = AsyncMock(
            return_value={"id": "123", "name": "test"}
        )

        # Call method
        result = await self.provider.get_account("test-account")

        # Verify
        self.mock_console.assert_called_once_with("http://localhost:8000")
        self.mock_console_instance.authenticate.assert_called_once_with(
            "admin", "password"
        )
        self.mock_console_instance.get_account.assert_called_once_with("test-account")
        assert result == {"id": "123", "name": "test"}

    @pytest.mark.asyncio
    async def test_create_account_calls_console(self):
        """Test that create_account calls Console.create_account."""
        # Setup mock
        self.mock_console_instance.authenticate = AsyncMock()
        self.mock_console_instance.create_account = AsyncMock(
            return_value={"id": "456", "name": "new-account"}
        )

        # Call method with required parameters
        result = await self.provider.create_account(
            "new-account", "<EMAIL>", 1000, "password123"
        )

        # Verify
        self.mock_console.assert_called_once_with(self.config["s3_endpoint_url"])
        self.mock_console_instance.authenticate.assert_called_once_with(
            "admin", "password"
        )
        self.mock_console_instance.create_account.assert_called_once_with(
            "new-account", "<EMAIL>", 1000, "password123"
        )
        assert result == {"id": "456", "name": "new-account"}

    @pytest.mark.asyncio
    async def test_delete_account_user_calls_console(self):
        """Test that delete_account_user calls Console.delete_account_user."""
        # Setup mock
        self.mock_console_instance.authenticate = AsyncMock()
        self.mock_console_instance.delete_account_user = AsyncMock(return_value=None)

        # Call method
        result = await self.provider.delete_account_user("test-account")

        # Verify
        self.mock_console.assert_called_once_with("http://localhost:8000")
        self.mock_console_instance.authenticate.assert_called_once_with(
            "admin", "password"
        )
        self.mock_console_instance.delete_account_user.assert_called_once_with(
            "test-account"
        )
        assert result is None


class TestScalityS3Provider:
    """Test the ScalityS3Provider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.config = {"s3_endpoint_url": "http://localhost:8000"}
        self.provider = ScalityS3Provider(self.region, self.config)

    @patch("api.providers.scality_provider.boto3")
    def test_get_s3_client_creates_boto3_client(self, mock_boto3):
        """Test that get_s3_client creates a boto3 S3 client."""
        # Setup mock
        mock_client = Mock()
        mock_boto3.client.return_value = mock_client

        # Call method
        result = self.provider.get_s3_client("access_key", "secret_key")

        # Verify
        mock_boto3.client.assert_called_once_with(
            service_name="s3",
            endpoint_url="http://localhost:8000",
            aws_access_key_id="access_key",
            aws_secret_access_key="secret_key",
            region_name=self.region,
            verify=False,
        )
        assert result == mock_client


class TestScalityIAMProvider:
    """Test the ScalityIAMProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.endpoint_url = "http://localhost:8000"
        self.access_key = "test-access-key"
        self.secret_access_key = "test-secret-key"
        # Mock the Iam class to prevent actual IAM client creation
        with patch("api.providers.scality_provider.Iam") as mock_iam:
            mock_iam_instance = Mock()
            mock_iam.return_value = mock_iam_instance
            self.provider = ScalityIAMProvider(
                self.region, self.endpoint_url, self.access_key, self.secret_access_key
            )
            self.mock_iam_instance = mock_iam_instance

    def test_delete_users_calls_iam(self):
        """Test that delete_users calls IAM delete_users method."""
        # Call method
        self.provider.delete_users()

        # Verify that the delete_users method was called on the mocked iam instance
        self.mock_iam_instance.delete_users.assert_called_once()

    @patch("api.providers.scality_provider.Iam")
    @pytest.mark.asyncio
    async def test_cleanup_account_iam_calls_iam_methods(self, mock_iam):
        """Test that cleanup_account_iam calls appropriate IAM cleanup methods."""
        # Setup mock
        mock_client = Mock()
        mock_iam.return_value = mock_client

        # Call method
        await self.provider.cleanup_account_iam("access_key", "secret_key")

        # Verify - the method creates a new Iam instance with the account credentials
        mock_iam.assert_called_with(
            endpoint_url=self.endpoint_url,
            region=self.region,
            access_key="access_key",
            secret_access_key="secret_key",
        )
        # Verify all cleanup methods are called
        mock_client.remove_users_from_groups.assert_called_once()
        mock_client.detach_role_policies.assert_called_once()
        mock_client.detach_group_policies.assert_called_once()
        mock_client.detach_user_policies.assert_called_once()
        mock_client.delete_policy_versions.assert_called_once()
        mock_client.delete_policies.assert_called_once()
        mock_client.delete_roles.assert_called_once()
        mock_client.delete_groups.assert_called_once()
        mock_client.delete_users.assert_called_once()


class TestProviderIntegration:
    """Integration tests for provider interactions."""

    def setup_method(self):
        """Set up test fixtures."""
        # Clear factory cache to ensure fresh instances
        ProviderFactory._instances = {}
        self.factory = ProviderFactory()

    @pytest.mark.asyncio
    async def test_console_account_creation_workflow(self):
        """Test the complete account creation workflow using providers."""
        # Setup mocks at class level before provider creation
        with (
            patch("api.providers.scality_provider.VaultClient") as mock_vault_client,
            patch("api.providers.scality_provider.Console") as mock_console,
        ):
            # Clear factory cache to ensure fresh instances with mocked classes
            ProviderFactory._instances = {}

            # Setup VaultClient mock
            mock_vault = Mock()
            mock_vault_client.return_value = mock_vault
            mock_vault.list_accounts = Mock(
                return_value={"accounts": [], "isTruncated": False}
            )  # Account doesn't exist

            # Setup Console mock
            mock_console_client = Mock()
            mock_console.return_value = mock_console_client
            mock_console_client.authenticate = AsyncMock()
            mock_console_client.create_account = AsyncMock(
                return_value={"id": "123", "name": "test-account"}
            )

            # Get providers (created with mocked classes)
            account_provider = self.factory.get_account_provider(Regions.fr_par)
            console_provider = self.factory.get_console_provider(Regions.fr_par)

            # Test workflow - expect NoSuchEntity when account doesn't exist
            from api.exceptions import NoSuchEntity

            with pytest.raises(NoSuchEntity, match="Missing account"):
                await account_provider.get_account("test-account")

            new_account = await console_provider.create_account(
                "test-account", "<EMAIL>", 1000, "password123"
            )
            assert new_account == {"id": "123", "name": "test-account"}

    @pytest.mark.asyncio
    async def test_console_account_deletion_workflow(self):
        """Test the complete account deletion workflow using providers."""
        # Setup mocks at class level before provider creation
        with (
            patch("api.providers.scality_provider.VaultClient") as mock_vault_client,
            patch("api.providers.scality_provider.Console") as mock_console,
            patch("api.providers.scality_provider.boto3") as mock_boto3,
            patch("api.providers.scality_provider.Iam") as mock_iam,
        ):
            # Clear factory cache to ensure fresh instances with mocked classes
            ProviderFactory._instances = {}

            # Setup VaultClient mock
            mock_vault = Mock()
            mock_vault_client.return_value = mock_vault
            mock_vault.generate_account_access_key = Mock(
                return_value={"access_key": "key", "secret_key": "secret"}
            )

            # Setup Console mock
            mock_console_client = Mock()
            mock_console.return_value = mock_console_client
            mock_console_client.authenticate = AsyncMock()
            mock_console_client.delete_account_user = AsyncMock(return_value=None)

            # Setup boto3 mock
            mock_s3_client = Mock()
            mock_boto3.client.return_value = mock_s3_client

            # Setup IAM mock
            mock_iam_client = Mock()
            mock_iam.return_value = mock_iam_client
            mock_iam_client.cleanup_account_iam = AsyncMock(return_value=None)

            # Get providers (created with mocked classes)
            account_provider = self.factory.get_account_provider(Regions.fr_par)
            console_provider = self.factory.get_console_provider(Regions.fr_par)
            s3_provider = self.factory.get_s3_provider(Regions.fr_par)
            iam_provider = self.factory.get_iam_provider(
                Regions.fr_par,
                "http://localhost:8000",
                "test-access-key",
                "test-secret-key",
            )

            # Test workflow
            credentials = await account_provider.generate_account_access_key(
                "test-account"
            )
            assert credentials == {"access_key": "key", "secret_key": "secret"}

            s3_client = s3_provider.get_s3_client(
                credentials["access_key"], credentials["secret_key"]
            )
            assert s3_client == mock_s3_client

            iam_cleanup_result = await iam_provider.cleanup_account_iam(
                credentials["access_key"], credentials["secret_key"]
            )
            assert iam_cleanup_result is None  # cleanup_account_iam returns None

            console_result = await console_provider.delete_account_user("test-account")
            assert console_result is None  # delete_account_user returns None


class TestProviderErrorHandling:
    """Test error handling in providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_account_provider_handles_vault_client_errors(
        self, mock_vault_client
    ):
        """Test that AccountProvider properly handles VaultClient errors."""
        # Setup mock to raise exception
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        mock_client.list_accounts = Mock(side_effect=Exception("Vault error"))

        config = {
            "vault_host": "localhost",
            "vault_port": "8500",
            "vault_access_key": "test-access-key",
            "vault_secret_key": "test-secret-key",
            "s3_endpoint_url": "http://localhost:8000",
        }
        provider = ScalityAccountProvider(self.region, config)

        # Test that exception is propagated
        with pytest.raises(Exception, match="Vault error"):
            await provider.get_account("test-account")

    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_console_provider_handles_console_errors(self, mock_console):
        """Test that ConsoleProvider properly handles Console errors."""
        # Setup mock to raise exception
        mock_client = Mock()
        mock_console.return_value = mock_client
        mock_client.authenticate = AsyncMock(side_effect=Exception("Console error"))
        mock_client.create_account = AsyncMock()

        config = {
            "s3_endpoint_url": "http://localhost:8000",
            "console_username": "admin",
            "console_password": "password",
        }
        provider = ScalityConsoleProvider(self.region, config)

        # Test that exception is propagated
        with pytest.raises(Exception, match="Console error"):
            await provider.create_account(
                "test-account", "<EMAIL>", 1000, "password123"
            )

    @patch("api.providers.scality_provider.boto3")
    def test_s3_provider_handles_boto3_errors(self, mock_boto3):
        """Test that S3Provider properly handles boto3 errors."""
        # Setup mock to raise exception
        mock_boto3.client.side_effect = Exception("S3 connection error")

        config = {"s3_endpoint_url": "http://localhost:8000"}
        provider = ScalityS3Provider(self.region, config)

        # Test that exception is propagated
        with pytest.raises(Exception, match="S3 connection error"):
            provider.get_s3_client("access_key", "secret_key")


if __name__ == "__main__":
    pytest.main([__file__])
