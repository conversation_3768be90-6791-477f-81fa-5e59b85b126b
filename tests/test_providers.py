"""
Unit tests for the provider abstraction layer.

This test suite validates the provider factory pattern and provider implementations
without requiring external services or API endpoints.
"""

from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest

from api.config import Regions
from api.providers.base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
)

# Import the provider classes
from api.providers.factory import ProviderFactory
from api.providers.scality_provider import (
    ScalityAccountProvider,
    ScalityConsoleProvider,
    ScalityIAMProvider,
    ScalityMetricsProvider,
    ScalityS3Provider,
)


class TestProviderFactory:
    """Test the ProviderFactory class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.factory = ProviderFactory()

    def test_get_account_provider_returns_scality_provider(self):
        """Test that get_account_provider returns ScalityAccountProvider."""
        provider = self.factory.get_account_provider(Regions.fr_par)
        assert isinstance(provider, ScalityAccountProvider)

    def test_get_metrics_provider_returns_scality_provider(self):
        """Test that get_metrics_provider returns ScalityMetricsProvider."""
        provider = self.factory.get_metrics_provider(Regions.fr_par)
        assert isinstance(provider, ScalityMetricsProvider)

    def test_get_console_provider_returns_scality_provider(self):
        """Test that get_console_provider returns ScalityConsoleProvider."""
        provider = self.factory.get_console_provider(Regions.fr_par)
        assert isinstance(provider, ScalityConsoleProvider)

    def test_get_s3_provider_returns_scality_provider(self):
        """Test that get_s3_provider returns ScalityS3Provider."""
        provider = self.factory.get_s3_provider(Regions.fr_par)
        assert isinstance(provider, ScalityS3Provider)

    def test_get_iam_provider_returns_scality_provider(self):
        """Test that get_iam_provider returns ScalityIAMProvider."""
        provider = self.factory.get_iam_provider(Regions.fr_par)
        assert isinstance(provider, ScalityIAMProvider)

    def test_different_regions_return_different_instances(self):
        """Test that different regions return different provider instances."""
        provider1 = self.factory.get_account_provider(Regions.fr_par)
        provider2 = self.factory.get_account_provider(Regions.fr_lyo)
        assert provider1 is not provider2

    def test_same_region_returns_same_instance(self):
        """Test that same region returns the same provider instance (caching)."""
        provider1 = self.factory.get_account_provider(Regions.fr_par)
        provider2 = self.factory.get_account_provider(Regions.fr_par)
        assert provider1 is provider2


class TestScalityAccountProvider:
    """Test the ScalityAccountProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.provider = ScalityAccountProvider(self.region)

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_get_account_calls_vault_client(self, mock_vault_client):
        """Test that get_account calls VaultClient.get_account."""
        # Setup mock
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        mock_client.get_account = AsyncMock(return_value={"name": "test-account"})

        # Call method
        result = await self.provider.get_account("test-account")

        # Verify
        mock_vault_client.assert_called_once_with(self.region)
        mock_client.get_account.assert_called_once_with("test-account")
        assert result == {"name": "test-account"}

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_search_accounts_calls_vault_client(self, mock_vault_client):
        """Test that search_accounts calls VaultClient.search_accounts."""
        # Setup mock
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        mock_client.search_accounts = AsyncMock(return_value=[{"name": "account1"}])

        # Call method
        result = await self.provider.search_accounts("test", True, False)

        # Verify
        mock_vault_client.assert_called_once_with(self.region)
        mock_client.search_accounts.assert_called_once_with("test", True, False)
        assert result == [{"name": "account1"}]

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_get_account_buckets_calls_vault_client(self, mock_vault_client):
        """Test that get_account_buckets calls VaultClient.get_account_buckets."""
        # Setup mock
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        mock_client.get_account_buckets = AsyncMock(return_value=["bucket1", "bucket2"])

        # Call method
        result = await self.provider.get_account_buckets("test-account")

        # Verify
        mock_vault_client.assert_called_once_with(self.region)
        mock_client.get_account_buckets.assert_called_once_with("test-account")
        assert result == ["bucket1", "bucket2"]

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_get_account_credentials_calls_vault_client(self, mock_vault_client):
        """Test that get_account_credentials calls VaultClient.get_account_credentials."""
        # Setup mock
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        expected_creds = {"access_key": "key", "secret_key": "secret"}
        mock_client.get_account_credentials = AsyncMock(return_value=expected_creds)

        # Call method
        result = await self.provider.get_account_credentials("test-account")

        # Verify
        mock_vault_client.assert_called_once_with(self.region)
        mock_client.get_account_credentials.assert_called_once_with("test-account")
        assert result == expected_creds


class TestScalityMetricsProvider:
    """Test the ScalityMetricsProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.provider = ScalityMetricsProvider(self.region)

    @patch("api.providers.scality_provider.Utapi")
    @pytest.mark.asyncio
    async def test_get_metrics_calls_utapi(self, mock_utapi):
        """Test that get_metrics calls Utapi.get_metrics."""
        # Setup mock
        mock_client = Mock()
        mock_utapi.return_value = mock_client
        mock_client.get_metrics = AsyncMock(return_value={"objects": 100})

        # Call method
        result = await self.provider.get_metrics("test-account", "bucket")

        # Verify
        mock_utapi.assert_called_once_with(self.region)
        mock_client.get_metrics.assert_called_once_with("test-account", "bucket")
        assert result == {"objects": 100}

    @patch("api.providers.scality_provider.Utapi")
    @pytest.mark.asyncio
    async def test_get_account_metrics_calls_utapi(self, mock_utapi):
        """Test that get_account_metrics calls Utapi.get_account_metrics."""
        # Setup mock
        mock_client = Mock()
        mock_utapi.return_value = mock_client
        mock_client.get_account_metrics = AsyncMock(return_value={"total_objects": 500})

        # Call method
        result = await self.provider.get_account_metrics("test-account")

        # Verify
        mock_utapi.assert_called_once_with(self.region)
        mock_client.get_account_metrics.assert_called_once_with("test-account")
        assert result == {"total_objects": 500}


class TestScalityConsoleProvider:
    """Test the ScalityConsoleProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.provider = ScalityConsoleProvider(self.region)

    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_get_account_calls_console(self, mock_console):
        """Test that get_account calls Console.get_account."""
        # Setup mock
        mock_client = Mock()
        mock_console.return_value = mock_client
        mock_client.get_account = AsyncMock(return_value={"id": "123", "name": "test"})

        # Call method
        result = await self.provider.get_account("test-account")

        # Verify
        mock_console.assert_called_once_with(self.region)
        mock_client.get_account.assert_called_once_with("test-account")
        assert result == {"id": "123", "name": "test"}

    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_create_account_calls_console(self, mock_console):
        """Test that create_account calls Console.create_account."""
        # Setup mock
        mock_client = Mock()
        mock_console.return_value = mock_client
        mock_client.create_account = AsyncMock(
            return_value={"id": "456", "name": "new-account"}
        )

        # Call method
        result = await self.provider.create_account("new-account")

        # Verify
        mock_console.assert_called_once_with(self.region)
        mock_client.create_account.assert_called_once_with("new-account")
        assert result == {"id": "456", "name": "new-account"}

    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_delete_account_user_calls_console(self, mock_console):
        """Test that delete_account_user calls Console.delete_account_user."""
        # Setup mock
        mock_client = Mock()
        mock_console.return_value = mock_client
        mock_client.delete_account_user = AsyncMock(return_value=True)

        # Call method
        result = await self.provider.delete_account_user("test-account")

        # Verify
        mock_console.assert_called_once_with(self.region)
        mock_client.delete_account_user.assert_called_once_with("test-account")
        assert result is True


class TestScalityS3Provider:
    """Test the ScalityS3Provider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.provider = ScalityS3Provider(self.region)

    @patch("api.providers.scality_provider.boto3")
    def test_get_client_creates_boto3_client(self, mock_boto3):
        """Test that get_client creates a boto3 S3 client."""
        # Setup mock
        mock_client = Mock()
        mock_boto3.client.return_value = mock_client

        # Call method
        result = self.provider.get_client("access_key", "secret_key")

        # Verify
        mock_boto3.client.assert_called_once_with(
            "s3",
            aws_access_key_id="access_key",
            aws_secret_access_key="secret_key",
            endpoint_url=self.region.value,
            config=self.provider.config,
        )
        assert result == mock_client

    @patch("api.providers.scality_provider.boto3")
    def test_get_resource_creates_boto3_resource(self, mock_boto3):
        """Test that get_resource creates a boto3 S3 resource."""
        # Setup mock
        mock_resource = Mock()
        mock_boto3.resource.return_value = mock_resource

        # Call method
        result = self.provider.get_resource("access_key", "secret_key")

        # Verify
        mock_boto3.resource.assert_called_once_with(
            "s3",
            aws_access_key_id="access_key",
            aws_secret_access_key="secret_key",
            endpoint_url=self.region.value,
            config=self.provider.config,
        )
        assert result == mock_resource


class TestScalityIAMProvider:
    """Test the ScalityIAMProvider class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par
        self.provider = ScalityIAMProvider(self.region)

    @patch("api.providers.scality_provider.Iam")
    def test_get_client_creates_iam_client(self, mock_iam):
        """Test that get_client creates an IAM client."""
        # Setup mock
        mock_client = Mock()
        mock_iam.return_value = mock_client

        # Call method
        result = self.provider.get_client("access_key", "secret_key")

        # Verify
        mock_iam.assert_called_once_with(self.region, "access_key", "secret_key")
        assert result == mock_client

    @patch("api.providers.scality_provider.Iam")
    @pytest.mark.asyncio
    async def test_cleanup_account_iam_calls_iam_methods(self, mock_iam):
        """Test that cleanup_account_iam calls appropriate IAM cleanup methods."""
        # Setup mock
        mock_client = Mock()
        mock_iam.return_value = mock_client
        mock_client.delete_account_users = AsyncMock(return_value=True)
        mock_client.delete_account_groups = AsyncMock(return_value=True)
        mock_client.delete_account_roles = AsyncMock(return_value=True)
        mock_client.delete_account_policies = AsyncMock(return_value=True)

        # Call method
        result = await self.provider.cleanup_account_iam("access_key", "secret_key")

        # Verify
        mock_iam.assert_called_once_with(self.region, "access_key", "secret_key")
        mock_client.delete_account_users.assert_called_once()
        mock_client.delete_account_groups.assert_called_once()
        mock_client.delete_account_roles.assert_called_once()
        mock_client.delete_account_policies.assert_called_once()
        assert result is True


class TestProviderIntegration:
    """Integration tests for provider interactions."""

    def setup_method(self):
        """Set up test fixtures."""
        self.factory = ProviderFactory()

    @patch("api.providers.scality_provider.VaultClient")
    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_console_account_creation_workflow(
        self, mock_console, mock_vault_client
    ):
        """Test the complete account creation workflow using providers."""
        # Setup mocks
        mock_vault = Mock()
        mock_vault_client.return_value = mock_vault
        mock_vault.get_account = AsyncMock(return_value=None)  # Account doesn't exist

        mock_console_client = Mock()
        mock_console.return_value = mock_console_client
        mock_console_client.create_account = AsyncMock(
            return_value={"id": "123", "name": "test-account"}
        )

        # Get providers
        account_provider = self.factory.get_account_provider(Regions.fr_par)
        console_provider = self.factory.get_console_provider(Regions.fr_par)

        # Test workflow
        existing_account = await account_provider.get_account("test-account")
        assert existing_account is None

        new_account = await console_provider.create_account("test-account")
        assert new_account == {"id": "123", "name": "test-account"}

    @patch("api.providers.scality_provider.VaultClient")
    @patch("api.providers.scality_provider.Console")
    @patch("api.providers.scality_provider.boto3")
    @patch("api.providers.scality_provider.Iam")
    @pytest.mark.asyncio
    async def test_console_account_deletion_workflow(
        self, mock_iam, mock_boto3, mock_console, mock_vault_client
    ):
        """Test the complete account deletion workflow using providers."""
        # Setup mocks
        mock_vault = Mock()
        mock_vault_client.return_value = mock_vault
        mock_vault.get_account_credentials = AsyncMock(
            return_value={"access_key": "key", "secret_key": "secret"}
        )

        mock_console_client = Mock()
        mock_console.return_value = mock_console_client
        mock_console_client.delete_account_user = AsyncMock(return_value=True)

        mock_s3_client = Mock()
        mock_boto3.client.return_value = mock_s3_client

        mock_iam_client = Mock()
        mock_iam.return_value = mock_iam_client
        mock_iam_client.delete_account_users = AsyncMock(return_value=True)
        mock_iam_client.delete_account_groups = AsyncMock(return_value=True)
        mock_iam_client.delete_account_roles = AsyncMock(return_value=True)
        mock_iam_client.delete_account_policies = AsyncMock(return_value=True)

        # Get providers
        account_provider = self.factory.get_account_provider(Regions.fr_par)
        console_provider = self.factory.get_console_provider(Regions.fr_par)
        s3_provider = self.factory.get_s3_provider(Regions.fr_par)
        iam_provider = self.factory.get_iam_provider(Regions.fr_par)

        # Test workflow
        credentials = await account_provider.get_account_credentials("test-account")
        assert credentials == {"access_key": "key", "secret_key": "secret"}

        s3_client = s3_provider.get_client(
            credentials["access_key"], credentials["secret_key"]
        )
        assert s3_client == mock_s3_client

        iam_cleanup_result = await iam_provider.cleanup_account_iam(
            credentials["access_key"], credentials["secret_key"]
        )
        assert iam_cleanup_result is True

        console_result = await console_provider.delete_account_user("test-account")
        assert console_result is True


class TestProviderErrorHandling:
    """Test error handling in providers."""

    def setup_method(self):
        """Set up test fixtures."""
        self.region = Regions.fr_par

    @patch("api.providers.scality_provider.VaultClient")
    @pytest.mark.asyncio
    async def test_account_provider_handles_vault_client_errors(
        self, mock_vault_client
    ):
        """Test that AccountProvider properly handles VaultClient errors."""
        # Setup mock to raise exception
        mock_client = Mock()
        mock_vault_client.return_value = mock_client
        mock_client.get_account = AsyncMock(side_effect=Exception("Vault error"))

        provider = ScalityAccountProvider(self.region)

        # Test that exception is propagated
        with pytest.raises(Exception, match="Vault error"):
            await provider.get_account("test-account")

    @patch("api.providers.scality_provider.Console")
    @pytest.mark.asyncio
    async def test_console_provider_handles_console_errors(self, mock_console):
        """Test that ConsoleProvider properly handles Console errors."""
        # Setup mock to raise exception
        mock_client = Mock()
        mock_console.return_value = mock_client
        mock_client.create_account = AsyncMock(side_effect=Exception("Console error"))

        provider = ScalityConsoleProvider(self.region)

        # Test that exception is propagated
        with pytest.raises(Exception, match="Console error"):
            await provider.create_account("test-account")

    @patch("api.providers.scality_provider.boto3")
    def test_s3_provider_handles_boto3_errors(self, mock_boto3):
        """Test that S3Provider properly handles boto3 errors."""
        # Setup mock to raise exception
        mock_boto3.client.side_effect = Exception("S3 connection error")

        provider = ScalityS3Provider(self.region)

        # Test that exception is propagated
        with pytest.raises(Exception, match="S3 connection error"):
            provider.get_client("access_key", "secret_key")


if __name__ == "__main__":
    pytest.main([__file__])
