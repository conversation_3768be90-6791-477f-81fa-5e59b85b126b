# Metrics Functional Tests

This document describes the comprehensive functional test suite for the JSO metrics endpoints.

## Overview

The metrics test suite (`test_metrics.py`) provides comprehensive functional testing for all three metrics endpoints:
- `/metrics/account/{account_id}` - Account-level metrics
- `/metrics/user/{user}` - User-level metrics  
- `/metrics/bucket/{bucket}` - Bucket-level metrics

## Test Categories

### 1. **Account Metrics Tests**
- ✅ `test_account_metrics_current` - Current timeframe (last 15 minutes)
- ✅ `test_account_metrics_week` - Weekly timeframe (last 7 days)
- ✅ `test_account_metrics_month` - Monthly timeframe (last 30 days)
- ✅ `test_account_metrics_year` - Yearly timeframe (last 365 days)
- ✅ `test_account_metrics_custom` - Custom timeframe with start/end timestamps
- ✅ `test_account_metrics_human_readable` - Human-readable format output
- ✅ `test_account_metrics_multiple_timeframes` - Multiple timeframes in single request
- ✅ `test_account_metrics_no_timeframe_error` - Error handling for missing timeframes

### 2. **User Metrics Tests**
- ✅ `test_user_metrics_current` - Current timeframe for user metrics
- ✅ `test_user_metrics_custom` - Custom timeframe for user metrics

### 3. **Bucket Metrics Tests**
- ✅ `test_bucket_metrics_current` - Current timeframe for bucket metrics
- ✅ `test_bucket_metrics_week` - Weekly timeframe for bucket metrics

### 4. **Error Handling Tests**
- ✅ `test_account_metrics_invalid_account` - Invalid account ID handling
- ✅ `test_user_metrics_invalid_user` - Invalid user handling
- ✅ `test_bucket_metrics_invalid_bucket` - Invalid bucket handling

### 5. **Performance Tests**
- ✅ `test_metrics_response_time` - Response time validation (< 10 seconds)

## Test Data Validation

Each test validates the complete metrics response structure:

```python
{
    "accountId": "string",      # For account metrics
    "userId": "string",         # For user metrics  
    "bucketName": "string",     # For bucket metrics
    "metrics": [
        {
            "interval": "current|week|month|year|custom",
            "startDate": "ISO datetime string",
            "endDate": "ISO datetime string", 
            "storageUtilized": "number",
            "numberOfObjects": "number",
            "numberOfOperations": {
                "operation_name": "count",
                ...
            }
        }
    ]
}
```

## Environment Setup

### Required Environment Variables

```bash
# Test Data (no authentication required for metrics endpoints)
ACCOUNT_ID="test-account-id"
TEST_USER="test-username"
TEST_BUCKET="test-bucket-name"
REGION="fr-lab"
```

### Setup Script

Use the provided environment setup script:

```bash
source tests/set_env_metrics.sh
```

## Running Tests

### All Metrics Tests
```bash
just test-metrics
```

### Specific Test Method
```bash
just test-metrics-method test_account_metrics_current
```

### Individual Test Categories
```bash
# Account metrics only
python3 -m unittest tests.test_metrics.TestMetrics.test_account_metrics_current

# User metrics only  
python3 -m unittest tests.test_metrics.TestMetrics.test_user_metrics_current

# Bucket metrics only
python3 -m unittest tests.test_metrics.TestMetrics.test_bucket_metrics_current
```

## Test Features

### 🔐 **Authentication**
- Automatic OAuth2 token acquisition using client credentials
- Token reuse across test methods
- Proper authorization headers for all requests

### 🔄 **Retry Logic**
- Automatic retry for transient failures (500, 423 status codes)
- Configurable backoff strategy
- Resilient to temporary network issues

### ✅ **Response Validation**
- Complete JSON structure validation
- Type checking for all fields
- Business logic validation (timeframes, intervals)
- Error response validation

### ⚡ **Performance Monitoring**
- Response time tracking
- Performance regression detection
- Timeout handling

### 🛡️ **Error Handling**
- Invalid input testing
- Non-existent resource testing
- Malformed request testing
- Network error resilience

## Integration with Provider Abstraction

The metrics tests validate the complete provider abstraction integration:

1. **ProviderFactory Usage**: Tests confirm metrics endpoints use `ProviderFactory.get_metrics_provider()`
2. **Multi-Region Support**: Tests validate region-specific provider instantiation
3. **Provider Interface**: Tests validate `BaseMetricsProvider.get_metrics()` implementation
4. **Error Propagation**: Tests validate proper error handling through provider layer

## Test Data Requirements

### Account Metrics
- Valid account ID that exists in the test region
- Account should have some historical data for meaningful tests

### User Metrics  
- Valid username that exists in the test account
- User should have some S3 activity for meaningful tests

### Bucket Metrics
- Valid bucket name that exists in the test account
- Bucket should have some objects/operations for meaningful tests

## Continuous Integration

The metrics tests are integrated into the main test suite:

```bash
# Run all tests including metrics
just tests

# Run only metrics tests in CI
just test-metrics
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify `CLIENT_ID_JSO` and `CLIENT_SECRET_JSO` are correct
   - Check token endpoint URL is accessible
   - Ensure client has proper permissions

2. **Test Data Issues**
   - Verify `ACCOUNT_ID`, `TEST_USER`, `TEST_BUCKET` exist
   - Check region configuration matches test data location
   - Ensure test data has some activity for meaningful metrics

3. **Network Issues**
   - Check API server is running and accessible
   - Verify firewall/proxy settings
   - Review retry configuration if needed

4. **Response Validation Failures**
   - Check API response format matches expected schema
   - Verify provider implementation returns correct data structure
   - Review logs for provider-specific errors

### Debug Mode

Run tests with verbose output:

```bash
python3 -m unittest -v tests.test_metrics.TestMetrics
```

## Future Enhancements

Potential test suite improvements:

- **Load Testing**: High-volume metrics requests
- **Concurrent Testing**: Multiple simultaneous requests
- **Data Validation**: Verify metrics accuracy against known data
- **Provider-Specific Tests**: Test different provider implementations
- **Caching Tests**: Validate metrics caching behavior
- **Time Zone Tests**: Validate timezone handling in custom timeframes
