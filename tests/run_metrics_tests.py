#!/usr/bin/env python3
"""
Metrics Test Runner

This script provides a convenient way to run metrics functional tests
with proper environment setup and reporting.
"""

import os
import sys
import time
import unittest
from typing import Optional


def check_environment() -> bool:
    """Check if required environment variables are set"""
    required_vars = ["ACCOUNT_ID", "TEST_USER", "TEST_BUCKET", "REGION"]

    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 Run: source tests/set_env_metrics.sh")
        print("   Then set the missing variables in your environment")
        return False

    print("✅ All required environment variables are set")
    return True


def run_test_class(test_class: Optional[str] = None) -> bool:
    """Run metrics tests with optional test class filter"""

    if not check_environment():
        return False

    print(f"\n🧪 Running Metrics Functional Tests")
    print("=" * 50)

    # Import test module
    try:
        from test_metrics import TestMetrics
    except ImportError as e:
        print(f"❌ Failed to import test module: {e}")
        return False

    # Create test suite
    loader = unittest.TestLoader()

    if test_class:
        # Run specific test method
        try:
            suite = loader.loadTestsFromName(f"test_metrics.TestMetrics.{test_class}")
        except AttributeError:
            print(f"❌ Test method '{test_class}' not found")
            return False
    else:
        # Run all tests
        suite = loader.loadTestsFromTestCase(TestMetrics)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout, buffer=True)

    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()

    # Print summary
    print("\n" + "=" * 50)
    print(f"📊 Test Summary")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"   Duration: {end_time - start_time:.2f}s")

    if result.failures:
        print(f"\n❌ Failed Tests:")
        for test, traceback in result.failures:
            print(f"   - {test}")

    if result.errors:
        print(f"\n💥 Error Tests:")
        for test, traceback in result.errors:
            print(f"   - {test}")

    success = len(result.failures) == 0 and len(result.errors) == 0

    if success:
        print(f"\n✅ All metrics tests passed!")
    else:
        print(f"\n❌ Some metrics tests failed")

    return success


def list_available_tests():
    """List all available test methods"""
    try:
        from test_metrics import TestMetrics

        print("📋 Available Metrics Test Methods:")
        print("=" * 40)

        test_methods = [
            method for method in dir(TestMetrics) if method.startswith("test_")
        ]

        categories = {
            "Account Metrics": [],
            "User Metrics": [],
            "Bucket Metrics": [],
            "Error Handling": [],
            "Performance": [],
        }

        for method in test_methods:
            if "account" in method:
                categories["Account Metrics"].append(method)
            elif "user" in method:
                categories["User Metrics"].append(method)
            elif "bucket" in method:
                categories["Bucket Metrics"].append(method)
            elif "invalid" in method or "error" in method:
                categories["Error Handling"].append(method)
            elif "performance" in method or "time" in method:
                categories["Performance"].append(method)

        for category, methods in categories.items():
            if methods:
                print(f"\n{category}:")
                for method in methods:
                    print(f"  - {method}")

        print(f"\n💡 Run specific test:")
        print(f"   python3 tests/run_metrics_tests.py <test_method_name>")

    except ImportError as e:
        print(f"❌ Failed to import test module: {e}")


def main():
    """Main entry point"""

    if len(sys.argv) > 1:
        if sys.argv[1] == "--list":
            list_available_tests()
            return
        elif sys.argv[1] == "--help":
            print("Metrics Test Runner")
            print("==================")
            print("Usage:")
            print(
                "  python3 tests/run_metrics_tests.py                    # Run all tests"
            )
            print(
                "  python3 tests/run_metrics_tests.py <test_method>      # Run specific test"
            )
            print(
                "  python3 tests/run_metrics_tests.py --list             # List available tests"
            )
            print(
                "  python3 tests/run_metrics_tests.py --help             # Show this help"
            )
            print("\nExamples:")
            print("  python3 tests/run_metrics_tests.py test_account_metrics_current")
            print("  python3 tests/run_metrics_tests.py test_user_metrics_custom")
            return
        else:
            # Run specific test
            test_method = sys.argv[1]
            success = run_test_class(test_method)
    else:
        # Run all tests
        success = run_test_class()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
