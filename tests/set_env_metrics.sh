#!/bin/bash

# Metrics Test Environment Configuration
# This file sets up environment variables specifically for metrics functional tests

# Base API Configuration
export GATEWAY_API_URL="https://api.jaguar-network.com"
export IDENTITY_PROVIDER_URL="https://auth.jaguar-network.com/realms/jaguar-network"
export IDENTITY_PROVIDER_REALM="jaguar-network"
export IDENTITY_PROVIDER_TOKEN_URL="https://auth.jaguar-network.com/realms/jaguar-network/protocol/openid-connect/token"

# Test Region Configuration
export REGION="fr-lab"

# No authentication required for metrics endpoints

# Test Data Configuration
# These should be set to valid test data in your environment
# export ACCOUNT_ID="test-account-id"
# export TEST_USER="test-user"
# export TEST_BUCKET="test-bucket"

# Optional: Override default test server URL
# export TEST_SERVER_URL="http://localhost:8071"

# UV Configuration for Python package management
export UV_VERSION="0.5.11"

# Development settings
export PYTHONPATH="."

echo "Metrics test environment configured!"
echo "Make sure to set the following environment variables:"
echo "  - ACCOUNT_ID"
echo "  - TEST_USER"
echo "  - TEST_BUCKET"
echo ""
echo "Run metrics tests with:"
echo "  source tests/set_env_metrics.sh"
echo "  just test-metrics"
