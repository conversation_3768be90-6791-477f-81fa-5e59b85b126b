#!/usr/bin/env python3
"""
Metrics Test Validation Script

This script validates the metrics test suite structure and completeness
without requiring external dependencies or running actual tests.
"""

import ast
import os
from pathlib import Path
from typing import Dict, List, Set


def analyze_test_file(file_path: Path) -> Dict:
    """Analyze the metrics test file using AST parsing"""

    if not file_path.exists():
        return {"error": f"Test file not found: {file_path}"}

    try:
        with open(file_path, "r") as f:
            content = f.read()

        tree = ast.parse(content)

        results = {
            "test_methods": [],
            "helper_methods": [],
            "imports": [],
            "class_found": False,
            "setup_methods": [],
            "validation_methods": [],
        }

        for node in ast.walk(tree):
            # Check imports
            if isinstance(node, ast.Import):
                for alias in node.names:
                    results["imports"].append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    results["imports"].append(f"{module}.{alias.name}")

            # Check class and methods
            elif isinstance(node, ast.ClassDef):
                if node.name == "TestMetrics":
                    results["class_found"] = True

                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_name = item.name

                            if method_name.startswith("test_"):
                                results["test_methods"].append(method_name)
                            elif method_name.startswith("setUp"):
                                results["setup_methods"].append(method_name)
                            elif method_name.startswith("_validate"):
                                results["validation_methods"].append(method_name)
                            elif method_name.startswith("_"):
                                results["helper_methods"].append(method_name)

        return results

    except Exception as e:
        return {"error": f"Failed to parse test file: {e}"}


def validate_test_coverage() -> Dict:
    """Validate test coverage for metrics endpoints"""

    expected_tests = {
        "account_metrics": [
            "test_account_metrics_current",
            "test_account_metrics_week",
            "test_account_metrics_month",
            "test_account_metrics_year",
            "test_account_metrics_custom",
            "test_account_metrics_human_readable",
            "test_account_metrics_multiple_timeframes",
            "test_account_metrics_no_timeframe_error",
            "test_account_metrics_invalid_account",
        ],
        "user_metrics": [
            "test_user_metrics_current",
            "test_user_metrics_custom",
            "test_user_metrics_invalid_user",
        ],
        "bucket_metrics": [
            "test_bucket_metrics_current",
            "test_bucket_metrics_week",
            "test_bucket_metrics_invalid_bucket",
        ],
        "performance": ["test_metrics_response_time"],
    }

    return expected_tests


def check_required_imports() -> Set[str]:
    """Check for required imports in metrics tests"""

    required_imports = {
        "os",
        "time",
        "unittest",
        "datetime.datetime",
        "datetime.timedelta",
        "requests",
        "dotenv.load_dotenv",
        "requests.adapters.HTTPAdapter",
        "requests.packages.urllib3.util.retry.Retry",
    }

    return required_imports


def validate_environment_setup() -> Dict:
    """Validate environment setup files"""

    results = {
        "env_script_exists": False,
        "env_script_executable": False,
        "required_vars_documented": False,
    }

    env_script = Path("set_env_metrics.sh")
    if env_script.exists():
        results["env_script_exists"] = True
        results["env_script_executable"] = os.access(env_script, os.X_OK)

        # Check if required variables are documented
        try:
            with open(env_script, "r") as f:
                content = f.read()
                required_vars = [
                    "CLIENT_ID_JSO",
                    "CLIENT_SECRET_JSO",
                    "ACCOUNT_ID",
                    "TEST_USER",
                    "TEST_BUCKET",
                    "REGION",
                ]

                documented_vars = sum(1 for var in required_vars if var in content)
                results["required_vars_documented"] = (
                    documented_vars >= len(required_vars) * 0.8
                )

        except Exception:
            pass

    return results


def main():
    """Main validation function"""

    print("🔍 Validating Metrics Test Suite")
    print("=" * 50)

    # Analyze test file
    test_file = Path("test_metrics.py")
    analysis = analyze_test_file(test_file)

    if "error" in analysis:
        print(f"❌ {analysis['error']}")
        return False

    # Check test class
    if analysis["class_found"]:
        print("✅ TestMetrics class found")
    else:
        print("❌ TestMetrics class not found")
        return False

    # Check test methods
    test_methods = analysis["test_methods"]
    print(f"📊 Found {len(test_methods)} test methods")

    # Validate test coverage
    expected_tests = validate_test_coverage()
    total_expected = sum(len(tests) for tests in expected_tests.values())

    print(f"\n📋 Test Coverage Analysis:")

    missing_tests = []
    for category, expected in expected_tests.items():
        found = [test for test in test_methods if any(exp in test for exp in expected)]
        missing = [test for test in expected if test not in test_methods]

        print(f"  {category.replace('_', ' ').title()}:")
        print(f"    Expected: {len(expected)}")
        print(f"    Found: {len(found)}")

        if missing:
            print(f"    Missing: {missing}")
            missing_tests.extend(missing)
        else:
            print(f"    ✅ Complete")

    # Check imports
    required_imports = check_required_imports()
    found_imports = set(analysis["imports"])
    missing_imports = required_imports - found_imports

    print(f"\n📦 Import Analysis:")
    print(f"  Required imports: {len(required_imports)}")
    print(f"  Found imports: {len(found_imports)}")

    if missing_imports:
        print(f"  ⚠️  Missing imports: {missing_imports}")
    else:
        print(f"  ✅ All required imports found")

    # Check helper methods
    helper_methods = analysis["helper_methods"]
    validation_methods = analysis["validation_methods"]

    print(f"\n🛠️  Helper Methods:")
    print(f"  Helper methods: {len(helper_methods)}")
    print(f"  Validation methods: {len(validation_methods)}")

    if "_make_request" in helper_methods:
        print("  ✅ Request helper found")
    else:
        print("  ⚠️  Request helper missing")

    if "_validate_metrics_response" in validation_methods:
        print("  ✅ Response validation helper found")
    else:
        print("  ⚠️  Response validation helper missing")

    # Check setup methods
    setup_methods = analysis["setup_methods"]
    if "setUpClass" in setup_methods:
        print("  ✅ Class setup method found")
    else:
        print("  ⚠️  Class setup method missing")

    # Validate environment setup
    env_validation = validate_environment_setup()

    print(f"\n🌍 Environment Setup:")
    if env_validation["env_script_exists"]:
        print("  ✅ Environment script exists")
        if env_validation["env_script_executable"]:
            print("  ✅ Environment script is executable")
        else:
            print("  ⚠️  Environment script not executable")

        if env_validation["required_vars_documented"]:
            print("  ✅ Required variables documented")
        else:
            print("  ⚠️  Some required variables not documented")
    else:
        print("  ❌ Environment script missing")

    # Overall assessment
    print(f"\n📈 Overall Assessment:")

    issues = []
    if missing_tests:
        issues.append(f"{len(missing_tests)} missing tests")
    if missing_imports:
        issues.append(f"{len(missing_imports)} missing imports")
    if not env_validation["env_script_exists"]:
        issues.append("environment setup missing")

    if not issues:
        print("  ✅ Metrics test suite is complete and well-structured!")
        return True
    else:
        print(f"  ⚠️  Issues found: {', '.join(issues)}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
