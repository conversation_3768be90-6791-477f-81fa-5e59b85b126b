[project]
name = "jso"
version = "2.0.0"
description = "Module for JSO operations (Object Storage)"
readme = "README.md"
requires-python = ">=3.10, <=3.11"
authors = [{ name = "<PERSON><PERSON> Manus", email = "<EMAIL>" }]
dependencies = [
    "uvicorn >= 0.32.0",
    "fastapi >= 0.115.5",
    "pydantic >=1.10.0",
    "fastapi-pagination >= 0.12.31",
    "urllib3 >=1.25.4,<1.27",
    "botocore >=1.31.16,<1.31.65",
    "aiobotocore >= 2.7.0",
    "types-aiobotocore-s3 >= 2.7.0",
    "aioboto3 >= 12.0.0",
    "boto3 >= 1.28",
    "aioprocessing >= 2.0.0",
    "httpx>=0.28.1",
    "tenacity >= 8.2.3",
    "requests >= 2.27",
    "humanfriendly",
    "pytz",
    "importlib-metadata >= 6.8.0",
    "sentry-sdk[fastapi] >= 1.40.5",
    "jnapi>=1.9.0",
    "scality>=1.2.0",
    "fp-aaa-kit>=0.11.0",
]

[tool.uv]
dev-dependencies = [
    "ruff",
    "rust-just",
    "python-dotenv",
    "snoop",
    "types-requests>=********",
    "mypy>=1.15.0",
    "types-humanfriendly>=10.0.1.20250319",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
]

#[tool.ruff.lint]
#select = ["E", "F", "W", "Q", "UP", "I", "N"]

[[tool.uv.index]]
name = "default"
url = "https://gitlab.as30781.net/api/v4/projects/719/packages/pypi/simple"
