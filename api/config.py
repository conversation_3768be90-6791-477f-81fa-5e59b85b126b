import configparser
import logging
from enum import Enum

logger = logging.getLogger(__name__)


class Config:
    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config.read_file(open("api/.config"))

    def get_config_parser(self):
        return self.config


class Regions(str, Enum):
    fr_lab = "fr-lab"
    fr_lyo = "fr-lyo"
    fr_mar = "fr-mar"
    fr_par = "fr-par"
