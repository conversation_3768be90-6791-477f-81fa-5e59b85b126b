from ..config import Config
from .huawei_provider import HuaweiProvider
from .providers.base import BaseAccountProvider, BaseMetricsProvider
from .scality_provider import ScalityProvider


class ProviderFactory:
    _providers = {
        "scality": ScalityProvider,
        "huawei": HuaweiProvider,
        # Add more providers as needed
    }

    @classmethod
    def get_account_provider(cls, region: str) -> BaseAccountProvider:
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            raise ValueError(f"Unsupported provider: {provider_type}")

        return cls._providers[provider_type].create_account_provider(
            region, config[region]
        )
    
    @classmethod
    def get_metrics_provider(cls, region: str) -> BaseMetricsProvider:
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            raise ValueError(f"Unsupported provider: {provider_type}")

        return cls._providers[provider_type].create_account_provider(
            region, config[region]
        )
