from ..config import Config
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
)


class ProviderFactory:
    """Factory class for creating provider instances"""

    _providers = {}  # Will be populated when providers are implemented
    _instances = {}  # Cache for provider instances by region and type

    @classmethod
    def get_account_provider(cls, region: str) -> BaseAccountProvider:
        """Get account provider for the specified region"""
        cache_key = f"account_{region}"
        if cache_key in cls._instances:
            return cls._instances[cache_key]

        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            # For now, we'll import and create Scality provider directly
            # This will be updated when we implement the Scality provider wrapper
            from .scality_provider import ScalityAccountProvider

            instance = ScalityAccountProvider(region, dict(config[region]))
            cls._instances[cache_key] = instance
            return instance

        provider_class = cls._providers[provider_type]
        instance = provider_class.create_account_provider(region, dict(config[region]))
        cls._instances[cache_key] = instance
        return instance

    @classmethod
    def get_metrics_provider(cls, region: str) -> BaseMetricsProvider:
        """Get metrics provider for the specified region"""
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            # For now, we'll import and create Scality provider directly
            from .scality_provider import ScalityMetricsProvider

            return ScalityMetricsProvider(region, dict(config[region]))

        provider_class = cls._providers[provider_type]
        return provider_class.create_metrics_provider(region, dict(config[region]))

    @classmethod
    def get_console_provider(cls, region: str) -> BaseConsoleProvider:
        """Get console provider for the specified region"""
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            # For now, we'll import and create Scality provider directly
            from .scality_provider import ScalityConsoleProvider

            return ScalityConsoleProvider(region, dict(config[region]))

        provider_class = cls._providers[provider_type]
        return provider_class.create_console_provider(region, dict(config[region]))

    @classmethod
    def get_s3_provider(cls, region: str) -> BaseS3Provider:
        """Get S3 provider for the specified region"""
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            # For now, we'll import and create Scality provider directly
            from .scality_provider import ScalityS3Provider

            return ScalityS3Provider(region, dict(config[region]))

        provider_class = cls._providers[provider_type]
        return provider_class.create_s3_provider(region, dict(config[region]))

    @classmethod
    def get_iam_provider(
        cls, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ) -> BaseIAMProvider:
        """Get IAM provider for the specified region"""
        config = Config().get_config_parser()
        provider_type = config[region].get(
            "provider", "scality"
        )  # Default to scality for backward compatibility

        if provider_type not in cls._providers:
            # For now, we'll import and create Scality provider directly
            from .scality_provider import ScalityIAMProvider

            return ScalityIAMProvider(
                region, endpoint_url, access_key, secret_access_key
            )

        provider_class = cls._providers[provider_type]
        return provider_class.create_iam_provider(
            region, endpoint_url, access_key, secret_access_key
        )
