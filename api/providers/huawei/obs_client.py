"""
Huawei Cloud Object Storage Service (OBS) client implementation.

This module provides a wrapper around Huawei OBS APIs for S3-compatible operations.
"""

import json
import logging
from typing import Any, Dict, Optional
from urllib.parse import urljoin

import httpx

logger = logging.getLogger(__name__)


class OBSClient:
    """Huawei Cloud Object Storage Service client"""

    def __init__(
        self,
        host: str,
        port: int,
        use_https: bool,
        username: str,
        password: str,
    ):
        """
        Initialize OBS client.

        Args:
            endpoint_url: Huawei OBS endpoint URL
            access_key: Access key ID
            secret_key: Secret access key
            region: Region name
        """
        self.host = host
        self.port = port
        self.use_https = use_https
        self.username = username
        self.password = password
        self.base_url = (
            f"{'https' if self.use_https else 'http'}://{self.host}:{self.port}/api/v2/"
        )
        self.headers = self._generate_token()

    async def _generate_token(self) -> Dict:
        """
        Generate authentication tokens for OBS operations.

        Returns:
            Authentication token
        """
        logger.info("Generating Huawei OBS token")

        payload = {"user_name": self.username, "password": self.password}
        headers = {"Content-Type": "application/json"}

        url_session = urljoin(self.base_url, "aa/sessions")
        try:
            r = requests.post(
                url_session, headers=headers, data=json.dumps(payload), verify=False
            )
            r.raise_for_status()
            x_auth_token = r.json()["data"]["x_auth_token"]
            x_csrf_token = r.json()["data"]["x_csrf_token"]
        except Exception as e:
            logger.error(f"Failed to generate Huawei OBS token: {str(e)}")
            raise e

        headers = {
            "X-Auth-Token": x_auth_token,
            "X-CSRF-Token": x_csrf_token,
            "Content-Type": "application/json",
        }

        return headers

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> Dict[str, Any]:
        """
        Create a new account.

        Args:
            account_name: Name of the account
            email: Email address for the account
            quota: Quota for the account
            password: Password for the account

        Returns:
            Created account information
        """
        logger.info(f"Creating Huawei OBS account: {account_name}")

        account_url = urljoin(base_url, "account/accounts")
        headers = {"Content-Type": "application/json"}
        payload = {"name": account_name}

        try:
            r = requests.post(account_url, json=payload, headers=headers, verify=False)
            r.raise_for_status()
        except Exception as e:
            logger.error(f"Failed to create Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            raise Exception(f"Failed to create Huawei OBS account: {account_name}")

        return r.json()["data"]

    async def get_accountid(self, account_name: str) -> Optional[str]:
        account_url = urljoin(self.base_url, "account/accounts")
        headers = {"Content-Type": "application/json"}
        payload = {"name": account_name}

        try:
            r = requests.get(account_url, params=payload, headers=headers, verify=False)
            r.raise_for_status()
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]["id"]

    async def _delete_unix_users(self, account_name: str) -> None:
        users_url = urljoin(self.base_url, "nas_protocol/unix_user")
        headers = {"Content-Type": "application/json"}
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        r = requests.get(users_url, params=params, headers=headers, verify=False)

        for user in r.json()["data"]:
            user_name = user["name"]
            params = f"name={user_name}&account_name={account_name}"
            try:
                r = requests.delete(
                    users_url, params=params, headers=headers, verify=False
                )
                r.raise_for_status()
            except Exception as e:
                logger.error(
                    f"Failed to delete user {user_name} for account {account_name}: {str(e)}"
                )
                raise e

    async def _delete_unix_groups(self, account_name: str) -> None:
        groups_url = urljoin(self.base_url, "nas_protocol/unix_group")
        headers = {"Content-Type": "application/json"}
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        r = requests.get(groups_url, params=params, headers=headers, verify=False)

        for group in r.json()["data"]:
            group_name = group["name"]
            params = f"name={group_name}&account_name={account_name}"
            try:
                r = requests.delete(
                    groups_url, params=params, headers=headers, verify=False
                )
                r.raise_for_status()
            except Exception as e:
                logger.error(
                    f"Failed to delete group {group_name} for account {account_name}: {str(e)}"
                )
                raise e

    async def delete_account(self, account_name: str) -> None:
        account_id = await self.get_accountid(account_name)
        await self._delete_unix_users(account_name)
        await self._delete_unix_groups(account_name)

        account_url = urljoin(self.base_url, "account/accounts")
        headers = {"Content-Type": "application/json"}
        payload = {"name": account_name}

        r = requests.delete(account_url, json=payload, headers=headers, verify=False)
