"""
Huawei Cloud Object Storage Service (OBS) client implementation.

This module provides a wrapper around Huawei OBS APIs for S3-compatible operations.
"""

import logging
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import httpx

from ...exceptions import AccessDenied, EntityAlreadyExists, NoSuchEntity

logger = logging.getLogger(__name__)


class OBSClient:
    """Huawei Cloud Object Storage Service client"""

    def __init__(
        self,
        host: str,
        port: str,
        use_https: bool,
        username: str,
        password: str,
    ):
        """
        Initialize OBS client.

        Args:
            endpoint_url: Huawei OBS endpoint URL
            access_key: Access key ID
            secret_key: Secret access key
            region: Region name
        """
        self.host = host
        self.port = port
        self.use_https = use_https
        self.username = username
        self.password = password
        self.base_url = (
            f"{'https' if self.use_https else 'http'}://{self.host}:{self.port}/api/v2/"
        )
        self.x_auth_token = ""
        self.x_csrf_token = ""

    @staticmethod
    def check_error(r: httpx.Response) -> None:
        if r.status_code == 409:
            raise EntityAlreadyExists("Entity already exists")

        r.raise_for_status()
        if r.json()["result"]["code"] != 0:
            logger.error(
                f"Failed to execute request: {r.json()['result']['description']}"
            )
            raise Exception(
                f"Failed to execute request: {r.json()['result']['description']}"
            )

    async def _generate_token(self) -> None:
        """
        Generate authentication tokens for OBS operations.

        Returns:
            Authentication token
        """
        logger.info("Generating Huawei OBS token")

        payload = {"user_name": self.username, "password": self.password}
        headers = {"Content-Type": "application/json"}

        url_session = urljoin(self.base_url, "aa/sessions")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.post(url_session, headers=headers, json=payload)
                OBSClient.check_error(r)
                response_data = r.json()
                self.x_auth_token = response_data["data"]["x_auth_token"]
                self.x_csrf_token = response_data["data"]["x_csrf_token"]
        except Exception as e:
            logger.error(f"Failed to generate Huawei OBS token: {str(e)}")
            raise e

    async def create_account(self, account_name: str) -> Dict[str, Any]:
        """
        Create a new account.

        Args:
            account_name: Name of the account

        Returns:
            Created account information
        """
        logger.info(f"Creating Huawei OBS account: {account_name}")

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.post(account_url, json=payload, headers=headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to create Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            raise Exception(f"Failed to create Huawei OBS account: {account_name}")

        return r.json()["data"]

    async def get_account(self, account_name: str) -> Optional[Dict]:
        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}

        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }

        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=payload, headers=headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]

    async def list_accounts(
        self, offset: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        account_url = urljoin(self.base_url, "account/accounts")
        params = {"range": f'{{"offset":{offset},"limit":{limit}}}'}
        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=params, headers=headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to list Huawei OBS accounts: {str(e)}")
            raise e

        return r.json()["data"]

    async def get_accountid(self, account_name: str) -> Optional[str]:
        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        try:
            async with httpx.AsyncClient(verify=False) as client:
                r = await client.get(account_url, params=payload, headers=headers)
                OBSClient.check_error(r)
        except Exception as e:
            logger.error(f"Failed to get Huawei OBS account: {str(e)}")
            raise e

        if r.json()["data"] == {}:
            logger.error(f"{r.json()['result']['description']}")
            return None

        return r.json()["data"]["id"]

    async def _delete_unix_users(self, account_name: str) -> None:
        users_url = urljoin(self.base_url, "nas_protocol/unix_user")
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.get(users_url, params=params, headers=headers)

            for user in r.json()["data"]:
                user_name = user["name"]
                delete_params = f"name={user_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        users_url, params=delete_params, headers=headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete user {user_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def _delete_unix_groups(self, account_name: str) -> None:
        groups_url = urljoin(self.base_url, "nas_protocol/unix_group")
        params = f"account_name={account_name}" + '&range={"offset": 0, "limit": 100}'
        await self._generate_token()
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.get(groups_url, params=params, headers=headers)

            for group in r.json()["data"]:
                group_name = group["name"]
                delete_params = f"name={group_name}&account_name={account_name}"
                try:
                    r = await client.delete(
                        groups_url, params=delete_params, headers=headers
                    )
                    OBSClient.check_error(r)
                except Exception as e:
                    logger.error(
                        f"Failed to delete group {group_name} for account {account_name}: {str(e)}"
                    )
                    raise e

    async def delete_account(self, account_name: str) -> None:
        await self._delete_unix_users(account_name)
        await self._delete_unix_groups(account_name)

        account_url = urljoin(self.base_url, "account/accounts")
        payload = {"name": account_name}
        headers = {
            "X-Auth-Token": self.x_auth_token,
            "X-CSRF-Token": self.x_csrf_token,
            "Content-Type": "application/json",
        }
        async with httpx.AsyncClient(verify=False) as client:
            r = await client.delete(account_url, headers=headers, json=payload)
            OBSClient.check_error(r)
