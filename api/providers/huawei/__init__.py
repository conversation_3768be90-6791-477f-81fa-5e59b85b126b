"""
Provider abstraction layer for multi-provider S3 support.
"""

from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)
from .factory import ProviderFactory
from .Huawei_provider import (
    HuaweiAccountProvider,
    HuaweiConsoleProvider,
    HuaweiIAMProvider,
    HuaweiMetricsProvider,
    HuaweiS3Provider,
)

__all__ = [
    "BaseAccountProvider",
    "BaseMetricsProvider",
    "BaseConsoleProvider",
    "BaseS3Provider",
    "BaseIAMProvider",
    "Instance",
    "ProviderFactory",
    "HuaweiAccountProvider",
    "HuaweiMetricsProvider",
    "HuaweiConsoleProvider",
    "HuaweiS3Provider",
    "HuaweiIAMProvider",
]
