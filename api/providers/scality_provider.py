"""
Scality provider implementation that wraps existing Scality-specific services.
"""

import logging
from typing import Any, Dict, List

import boto3
from scality.vault import VaultClient

from ..s3_console import Console
from ..s3_iam import Iam
from ..s3_utapi import Instance as UtapiInstance
from ..s3_utapi import Utapi
from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)

logger = logging.getLogger(__name__)


class ScalityAccountProvider(BaseAccountProvider):
    """Scality implementation of account provider using VaultClient"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.vault_client = VaultClient(
            host=config["vault_host"],
            port=int(config["vault_port"]),
            use_https=True,
            access_key=config["vault_access_key"],
            secret_access_key=config["vault_secret_key"],
        )

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> Dict[str, Any]:
        """Create account using VaultClient"""
        return self.vault_client.create_account(
            name=account_name,
            email=email,
            quota=quota,
            password=password,
        )

    async def delete_account(self, account_name: str) -> None:
        """Delete account using VaultClient"""
        self.vault_client.delete_account(name=account_name)

    async def get_account(
        self, account_name: str, starts_with: bool = False, ends_with: bool = False
    ) -> List[Dict[str, Any]]:
        """Get account using VaultClient"""
        if starts_with or ends_with:
            # For prefix/suffix searches, we need to list and filter
            accounts = self.vault_client.list_accounts()
            filtered_accounts = []
            for account in accounts:
                name = account.get("name", "")
                if starts_with and name.startswith(account_name):
                    filtered_accounts.append(account)
                elif ends_with and name.endswith(account_name):
                    filtered_accounts.append(account)
            return filtered_accounts
        else:
            # Direct account lookup
            account = self.vault_client.get_account(name=account_name)
            return [account] if account else []

    async def list_accounts(self, marker: str = "") -> Dict[str, Any]:
        """List accounts using VaultClient"""
        # VaultClient doesn't have pagination, so we'll return all accounts
        accounts = self.vault_client.list_accounts()
        return {
            "accounts": accounts,
            "marker": "",  # No more pages
        }

    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        """Generate access key using VaultClient"""
        return self.vault_client.generate_account_access_key(account_name)

    async def update_account_quota(self, account_name: str, quota_bytes: int) -> None:
        """Update account quota using VaultClient"""
        self.vault_client.update_account_quota(name=account_name, quota=quota_bytes)

    async def delete_account_quota(self, account_name: str) -> None:
        """Delete account quota using VaultClient"""
        self.vault_client.delete_account_quota(name=account_name)


class ScalityMetricsProvider(BaseMetricsProvider):
    """Scality implementation of metrics provider using Utapi"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.utapi = Utapi(region)
        # Mapping from our Instance enum to Utapi's Instance enum
        self._instance_mapping = {
            Instance.ACCOUNT: UtapiInstance.ACCOUNT,
            Instance.USER: UtapiInstance.USER,
            Instance.BUCKET: UtapiInstance.BUCKET,
        }

    async def get_metrics(
        self,
        instance_type: Instance,
        object_name: str,
        human: bool,
        selector: List[str] = None,
        custom_time: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """Get metrics using Utapi"""
        if selector is None:
            selector = []
        if custom_time is None:
            custom_time = {}

        # Convert our Instance enum to Utapi's Instance enum
        utapi_instance_type = self._instance_mapping[instance_type]

        return await self.utapi.get_metrics(
            instance_type=utapi_instance_type,
            object=object_name,
            human=human,
            selector=selector,
            custom_time=custom_time,
        )


class ScalityConsoleProvider(BaseConsoleProvider):
    """Scality implementation of console provider using Console"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.console = Console(config["s3_endpoint_url"])
        self.console_username = config["console_username"]
        self.console_password = config["console_password"]

    async def authenticate(self, console_username: str, console_password: str) -> None:
        """Authenticate with console"""
        await self.console.authenticate(console_username, console_password)

    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> Dict[str, Any]:
        """Create account via console"""
        await self.console.authenticate(self.console_username, self.console_password)
        return await self.console.create_account(account_name, email, quota, password)

    async def delete_account(self, account_name: str) -> None:
        """Delete account via console"""
        await self.console.authenticate(self.console_username, self.console_password)
        await self.console.delete_account(account_name)

    async def get_account(self, account_name: str) -> Dict[str, Any]:
        """Get account via console"""
        await self.console.authenticate(self.console_username, self.console_password)
        return await self.console.get_account(account_name)

    async def list_accounts(self, marker: str) -> Dict[str, Any]:
        """List accounts via console"""
        await self.console.authenticate(self.console_username, self.console_password)
        return await self.console.list_accounts(marker)


class ScalityS3Provider(BaseS3Provider):
    """Scality implementation of S3 provider using boto3"""

    def __init__(self, region: str, config: Dict[str, str]):
        super().__init__(region, config)
        self.endpoint_url = config["s3_endpoint_url"]

    def get_s3_client(self, access_key: str, secret_key: str):
        """Get S3 client for Scality"""
        return boto3.client(
            service_name="s3",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )

    def get_iam_client(self, access_key: str, secret_key: str):
        """Get IAM client for Scality"""
        return boto3.client(
            service_name="iam",
            endpoint_url=self.endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=self.region,
            verify=False,
        )


class ScalityIAMProvider(BaseIAMProvider):
    """Scality implementation of IAM provider using existing Iam class"""

    def __init__(
        self, region: str, endpoint_url: str, access_key: str, secret_access_key: str
    ):
        super().__init__(region, endpoint_url, access_key, secret_access_key)
        self.iam = Iam(region, endpoint_url, access_key, secret_access_key)

    def remove_users_from_groups(self) -> None:
        """Remove users from groups"""
        self.iam.remove_users_from_groups()

    def detach_role_policies(self) -> None:
        """Detach role policies"""
        self.iam.detach_role_policies()

    def detach_group_policies(self) -> None:
        """Detach group policies"""
        self.iam.detach_group_policies()

    def detach_user_policies(self) -> None:
        """Detach user policies"""
        self.iam.detach_user_policies()

    def delete_policy_versions(self) -> None:
        """Delete policy versions"""
        self.iam.delete_policy_versions()

    def delete_policies(self) -> None:
        """Delete policies"""
        self.iam.delete_policies()

    def delete_roles(self) -> None:
        """Delete roles"""
        self.iam.delete_roles()

    def delete_groups(self) -> None:
        """Delete groups"""
        self.iam.delete_groups()

    def delete_users(self) -> None:
        """Delete users"""
        self.iam.delete_users()
