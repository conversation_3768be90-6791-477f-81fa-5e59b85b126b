import calendar
import datetime
import hashlib
import hmac
import json
import logging
from enum import Enum, auto

import httpx
import humanfriendly  # type: ignore

from ...config import Config

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# Key derivation functions. See:
# http://docs.aws.amazon.com/general/latest/gr/signature-v4-examples.html#signature-v4-examples-python
def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


def getSignatureKey(key, date_stamp, regionName, serviceName):
    kDate = sign(("AWS4" + key).encode("utf-8"), date_stamp)
    kRegion = sign(kDate, regionName)
    kService = sign(kRegion, serviceName)
    kSigning = sign(kService, "aws4_request")
    return kSigning


class Instance(Enum):
    ACCOUNT = auto()
    USER = auto()
    BUCKET = auto()


class Utapi:
    CANONICAL_QUERYSTRING = "Action=ListMetrics&Version=********"
    CONTENT_TYPE = "application/x-amz-json-1.0"
    ALGORITHM = "AWS4-HMAC-SHA256"

    @staticmethod
    def get_start_time(t):
        start = t.replace(minute=t.minute - t.minute % 15, second=0, microsecond=0)
        return calendar.timegm(start.utctimetuple()) * 1000

    @staticmethod
    def get_end_time(t):
        end = t.replace(minute=t.minute - t.minute % 15, second=0, microsecond=0)
        return calendar.timegm(end.utctimetuple()) * 1000 - 1

    def __init__(self, region):
        self.region = region
        config = Config().get_config_parser()
        if region in config:
            self.host = config[region]["utapi_host"]
            self.port = config[region]["utapi_port"]
            self.utapi_access_key = config[region]["utapi_access_key"]
            self.utapi_secret_key = config[region]["utapi_secret_key"]
        else:
            raise ValueError(f"Missing config for region {region}")

    async def get_metrics(
        self,
        instance_type: Instance,
        object: str,
        human: bool,
        selector: list = [],
        custom_time: dict = {},
    ):
        """"""
        current_time = datetime.datetime.utcnow()
        amz_date = current_time.strftime("%Y%m%dT%H%M%SZ")
        date_stamp = current_time.strftime("%Y%m%d")

        canonical_uris = {
            Instance.ACCOUNT: "/accounts",
            Instance.USER: "/users",
            Instance.BUCKET: "/buckets",
        }
        canonical_uri = canonical_uris[instance_type]

        results = []
        for interval in ["current", "week", "month", "year", "custom"]:
            if interval not in selector:
                continue

            logger.info(f"UTAPI - Processing {interval} for {object}")

            start_time, end_time = self.get_intervals(
                custom_time, current_time, interval
            )

            # Request parameters for listing Utapi object metrics--passed in a JSON block.
            object_listing = {}
            if instance_type is Instance.ACCOUNT:
                object_listing = {
                    "accounts": [object],
                    "timeRange": [start_time, end_time],
                }
            elif instance_type is Instance.USER:
                object_listing = {
                    "users": [object],
                    "timeRange": [start_time, end_time],
                }
            elif instance_type is Instance.BUCKET:
                object_listing = {
                    "buckets": [object],
                    "timeRange": [start_time, end_time],
                }

            request_parameters = json.dumps(object_listing)

            payload_hash = hashlib.sha256(
                str(request_parameters).encode("utf-8")
            ).hexdigest()

            canonical_headers = "content-type:{0}\nhost:{1}\nx-amz-content-sha256:{2}\nx-amz-date:{3}\n".format(
                Utapi.CONTENT_TYPE, self.host + ":" + self.port, payload_hash, amz_date
            )

            signed_headers = "content-type;host;x-amz-content-sha256;x-amz-date"

            canonical_request = "{0}\n{1}\n{2}\n{3}\n{4}\n{5}".format(
                "POST",
                canonical_uri,
                Utapi.CANONICAL_QUERYSTRING,
                canonical_headers,
                signed_headers,
                payload_hash,
            )

            credential_scope = "{0}/{1}/{2}/aws4_request".format(
                date_stamp, self.region, "s3"
            )

            string_to_sign = "{0}\n{1}\n{2}\n{3}".format(
                Utapi.ALGORITHM,
                amz_date,
                credential_scope,
                hashlib.sha256(str(canonical_request).encode("utf-8")).hexdigest(),
            )

            signing_key = getSignatureKey(
                self.utapi_secret_key, date_stamp, self.region, "s3"
            )

            signature = hmac.new(
                signing_key, (string_to_sign).encode("utf-8"), hashlib.sha256
            ).hexdigest()

            authorization_header = (
                "{0} Credential={1}/{2}, SignedHeaders={3}, Signature={4}".format(
                    Utapi.ALGORITHM,
                    self.utapi_access_key,
                    credential_scope,
                    signed_headers,
                    signature,
                )
            )

            headers = {
                "Content-Type": Utapi.CONTENT_TYPE,
                "X-Amz-Content-Sha256": payload_hash,
                "X-Amz-Date": amz_date,
                "Authorization": authorization_header,
                # "Host": self.host,
            }

            endpoint = (
                "http://"
                + self.host
                + ":"
                + self.port
                + canonical_uri
                + "?"
                + Utapi.CANONICAL_QUERYSTRING
            )

            async with httpx.AsyncClient() as client:
                try:
                    r = await client.post(
                        endpoint,
                        data=request_parameters,  # type: ignore
                        headers=headers,
                        timeout=30.0,
                    )
                    r.raise_for_status()
                    logger.debug(str(r.json()))
                except Exception as e:
                    logging.error(request_parameters)
                    raise e

            for i in r.json():
                try:
                    i["numberOfObjects"][1]
                except Exception:
                    continue

                if interval in ["current", "custom"]:
                    delta = ""
                else:
                    delta = "(delta)"

                operations_count = sum(i["operations"].values(), start=0)

                if instance_type is Instance.ACCOUNT:
                    name = "accountId"
                elif instance_type is Instance.USER:
                    name = "userId"
                elif instance_type is Instance.BUCKET:
                    name = "bucketName"

                if not human:
                    results.append(
                        {
                            "interval": interval,
                            "startDate": datetime.datetime.utcfromtimestamp(
                                int(start_time / 1000)
                            ).isoformat(),
                            "endDate": datetime.datetime.utcfromtimestamp(
                                int(end_time / 1000)
                            ).isoformat(),
                            f"storageUtilized{delta}": i["storageUtilized"][1]
                            - (
                                0
                                if interval in ["current", "custom"]
                                else i["storageUtilized"][0]
                            ),
                            f"numberOfObjects{delta}": i["numberOfObjects"][1]
                            - (
                                0
                                if interval in ["current", "custom"]
                                else i["numberOfObjects"][0]
                            ),
                            "incomingBytes": i["incomingBytes"],
                            "outgoingBytes": i["outgoingBytes"],
                            "numberOfOperations": operations_count,
                            "operations": i["operations"],
                        }
                    )
                else:
                    storageUtilized = i["storageUtilized"][1] - (
                        0
                        if interval in ["current", "custom"]
                        else i["storageUtilized"][0]
                    )
                    h_storageUtilized = humanfriendly.format_size(
                        abs(storageUtilized), binary=True
                    )
                    if interval in ["current", "custom"]:
                        sign = ""
                    else:
                        sign = "+" if storageUtilized >= 0 else "-"
                    results.append(
                        {
                            "interval": interval,
                            "startDate": datetime.datetime.utcfromtimestamp(
                                int(start_time / 1000)
                            ).isoformat(),
                            "endDate": datetime.datetime.utcfromtimestamp(
                                int(end_time / 1000)
                            ).isoformat(),
                            f"storageUtilized{delta}": sign + h_storageUtilized,
                            f"numberOfObjects{delta}": humanfriendly.format_number(
                                i["numberOfObjects"][1]
                                - (
                                    0
                                    if interval in ["current", "custom"]
                                    else i["numberOfObjects"][0]
                                ),
                            ),
                            "incomingBytes": humanfriendly.format_size(
                                i["incomingBytes"], binary=True
                            ),
                            "outgoingBytes": humanfriendly.format_size(
                                i["outgoingBytes"], binary=True
                            ),
                            "operations_count": humanfriendly.format_number(
                                operations_count
                            ),
                            "numberOfOperations": i["operations"],
                        }
                    )

        final_result = {name: i[name], "metrics": results}  # type: ignore
        return final_result

    def get_intervals(self, custom_time, dt, interval):
        if interval == "custom":
            try:
                if custom_time["start_time"] >= custom_time["end_time"]:
                    raise ValueError("start_time must be lower than end_time")

                try:
                    start_time = datetime.datetime.utcfromtimestamp(
                        custom_time["start_time"]
                    )
                except ValueError:
                    # assume timestamp is in milliseconds
                    start_time = datetime.datetime.utcfromtimestamp(
                        int(custom_time["start_time"] / 1000)
                    )
                start_time = Utapi.get_start_time(start_time)
                try:
                    end_time = datetime.datetime.utcfromtimestamp(
                        custom_time["end_time"]
                    )
                except ValueError:
                    # assume timestamp is in milliseconds
                    end_time = datetime.datetime.utcfromtimestamp(
                        int(custom_time["end_time"] / 1000)
                    )
                end_time = Utapi.get_end_time(end_time)
            except Exception:
                logger.warning("Reverting to 'current' timeframe")
                start_time = Utapi.get_start_time(dt - datetime.timedelta(minutes=30))
                end_time = Utapi.get_end_time(dt - datetime.timedelta(minutes=15))
        else:
            interval_deltas = {
                "current": datetime.timedelta(minutes=30),
                "week": datetime.timedelta(days=7),
                "month": datetime.timedelta(days=30),
                "year": datetime.timedelta(days=365),
            }
            delta = interval_deltas.get(interval)
            if delta is None:
                raise ValueError("Interval undefined")

            start_time = Utapi.get_start_time(dt - delta)
            end_time = Utapi.get_end_time(dt - datetime.timedelta(minutes=15))
        return start_time, end_time
