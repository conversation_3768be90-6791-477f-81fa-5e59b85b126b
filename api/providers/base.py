from abc import ABC, abstractmethod
from typing import Any, Dict, List


class BaseAccountProvider(ABC):
    """Abstract base class for account management providers"""

    @abstractmethod
    async def create_account(
        self, account_name: str, email: str, quota: int, password: str
    ) -> Dict[str, Any]:
        pass

    @abstractmethod
    async def delete_account(self, account_name: str) -> None:
        pass

    @abstractmethod
    async def get_account(self, account_name: str) -> Dict[str, Any]:
        pass

    @abstractmethod
    async def generate_account_access_key(self, account_name: str) -> Dict[str, str]:
        pass

class BaseConsoleProvider(ABC):
    """Abstract base class for console providers"""

    @abstractmethod
    async def authenticate(self, console_username: str, console_password: str) -> None:
        pass

class BaseMetricsProvider(ABC):
    """Abstract base class for metrics providers"""

    @abstractmethod
    async def get_account_metrics(
        self, account_id: str, **kwargs
    ) -> List[Dict[str, Any]]:
        pass

    @abstractmethod
    async def get_bucket_metrics(
        self, bucket_name: str, **kwargs
    ) -> List[Dict[str, Any]]:
        pass

    @abstractmethod
    async def get_user_metrics(
        self, user_name: str, **kwargs
    ) -> List[Dict[str, Any]]:
        pass
