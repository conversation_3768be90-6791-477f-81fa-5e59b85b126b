"""
Provider abstraction layer for multi-provider S3 support.
"""

from .base import (
    BaseAccountProvider,
    BaseConsoleProvider,
    BaseIAMProvider,
    BaseMetricsProvider,
    BaseS3Provider,
    Instance,
)
from .factory import ProviderFactory
from .scality_provider import (
    ScalityAccountProvider,
    ScalityConsoleProvider,
    ScalityIAMProvider,
    ScalityMetricsProvider,
    ScalityS3Provider,
)

__all__ = [
    "BaseAccountProvider",
    "BaseMetricsProvider",
    "BaseConsoleProvider",
    "BaseS3Provider",
    "BaseIAMProvider",
    "Instance",
    "ProviderFactory",
    "ScalityAccountProvider",
    "ScalityMetricsProvider",
    "ScalityConsoleProvider",
    "ScalityS3Provider",
    "ScalityIAMProvider",
]
