from typing import Dict, Optional

from fastapi import Request
from fastapi.openapi.models import OAuthFlows as OAuthFlowsModel
from fastapi.security import OAuth2


class FakeOAuth2ClientCredentials(OAuth2):
    """
    Class used for authorization functionnality into swagger.
    Fakes user auth into FastApi. User Auth is managed by <PERSON>rakend
    """

    def __init__(
        self,
        token_url: str,
        scheme_name: Optional[str] = None,
        scopes: Optional[Dict[str, str]] = None,
        description: Optional[str] = None,
        auto_error: bool = True,
    ):
        if not scopes:
            scopes = {}
        flows = OAuthFlowsModel(
            clientCredentials={"tokenUrl": token_url, "scopes": scopes}  # type: ignore
        )
        super().__init__(
            flows=flows,
            scheme_name=scheme_name,
            description=description,
            auto_error=auto_error,
        )

    async def __call__(self, request: Request) -> Optional[str]:
        return "Fake"


oauth2_faker = FakeOAuth2ClientCredentials(
    token_url="/auth/realms/api-gw/protocol/openid-connect/token"
)
