import hashlib
import json
import logging
from enum import Enum
from typing import Any, Dict

from jnapi import Session as SessionNoAsync  # type: ignore
from jnapi_async import Session  # type: ignore
from pydantic import BaseSettings


class ApplicationSettings(BaseSettings):
    IDP_ROOT_URI: str

    class Config:
        case_sensitive = True


class AppSettings(BaseSettings):
    gateway_api_url: str
    identity_provider_url: str
    identity_provider_realm: str
    client_id: str
    client_secret: str


application_settings = ApplicationSettings()

"""
Module-defined variable that holds the application settings.
"""
_settings = AppSettings()  # type: ignore


# Shared logging handler config
_handler = logging.StreamHandler()
_handler.setFormatter(logging.Formatter("%(asctime)s %(levelname)s: %(message)s"))


def get_logger(name: str) -> logging.Logger:
    logger = logging.getLogger(name)
    logger.addHandler(_handler)
    logger.setLevel(logging.DEBUG)
    return logger


async def get_api_session() -> Session:
    """
    Creates a JN-API Session object from environment settings.
    :return: a configured Session object.
    """
    return Session(
        gateway_api_url=_settings.gateway_api_url,
        identity_provider_url=_settings.identity_provider_url,
        realm=_settings.identity_provider_realm,
        client_id=_settings.client_id,
        client_secret=_settings.client_secret,
    )


def get_api_session_no_async() -> SessionNoAsync:
    """
    Creates a JN-API Session object from environment settings.
    :return: a configured Session object.
    """
    return SessionNoAsync(
        gateway_api_url=_settings.gateway_api_url,
        identity_provider_url=_settings.identity_provider_url,
        realm=_settings.identity_provider_realm,
        client_id=_settings.client_id,
        client_secret=_settings.client_secret,
    )


class _EnumAwareJSONEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Enum):
            return o.value
        return super().default(o)


def pre_serialize_model(m):
    """
    Converts a BaseModel subclass instance to a regular Python dictionnary.
    Takes care of the enumerations, among other things.
    :param m: The model instance to serialize
    :type m: pydantic.BaseModel
    :return: The pre-serialized model
    :rtype: dict
    """
    return json.loads(json.dumps(m.dict(), cls=_EnumAwareJSONEncoder))


class Cache:
    """
    A simple in-memory cache implementation with dictionary key hashing.

    This cache allows storing and retrieving values using dictionary keys,
    with MD5 hashing to ensure consistent key representation regardless of dictionary order.

    Attributes:
        cache (dict): Internal storage for cached items.

    Methods:
        hit(key): Check if a key exists in the cache.
        get(key): Retrieve a value from the cache by key.
        set(key, value): Store a value in the cache with a given key.
        dict_hash(dictionary): Generate a consistent MD5 hash for a dictionary key.
    """

    def __init__(self):
        self.cache = {}

    def hit(self, key: Dict[str, Any]):
        key = self.dict_hash(key)
        return key in self.cache

    def get(self, key: Dict[str, Any]):
        key = self.dict_hash(key)
        return self.cache[key]

    def set(self, key: Dict[str, Any], value):
        key = self.dict_hash(key)
        self.cache[key] = value

    def dict_hash(self, dictionary: Dict[str, Any]) -> str:
        """MD5 hash of a dictionary."""
        dhash = hashlib.md5()
        # We need to sort arguments so {'a': 1, 'b': 2} is
        # the same as {'b': 2, 'a': 1}
        encoded = json.dumps(dictionary, sort_keys=True).encode()
        dhash.update(encoded)
        return dhash.hexdigest()
