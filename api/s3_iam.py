import logging

import boto3  # type: ignore
import urllib3  # type: ignore

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)  # type: ignore
# type: ignore # type: ignore
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class Iam:
    def __init__(self, region, endpoint_url, access_key, secret_access_key):
        self.iam_client = boto3.client(
            service_name="iam",
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_access_key,
            region_name=region,
            verify=False,
        )

    def remove_users_from_groups(self):
        try:
            for group in self.iam_client.list_groups()["Groups"]:
                for user in self.iam_client.get_group(GroupName=group["GroupName"])[
                    "Users"
                ]:
                    logger.debug(
                        "Remove user {} from group {}".format(
                            user["UserName"], group["GroupName"]
                        )
                    )
                    self.iam_client.remove_user_from_group(
                        GroupName=group["GroupName"], UserName=user["UserName"]
                    )
        except Exception as e:
            logger.warning(str(e))
            pass

    def detach_user_policies(self):
        try:
            for user in self.iam_client.list_users()["Users"]:
                for policy in self.iam_client.list_attached_user_policies(
                    UserName=user["UserName"]
                )["AttachedPolicies"]:
                    logger.debug(
                        "Detach policy {} from user {}".format(
                            policy["PolicyArn"].split("/")[1], user["UserName"]
                        )
                    )
                    self.iam_client.detach_user_policy(
                        UserName=user["UserName"], PolicyArn=policy["PolicyArn"]
                    )

        except Exception as e:
            logger.warning(str(e))
            pass

    def detach_group_policies(self):
        try:
            for group in self.iam_client.list_groups()["Groups"]:
                for policy in self.iam_client.list_attached_group_policies(
                    GroupName=group["GroupName"]
                )["AttachedPolicies"]:
                    logger.debug(
                        "Detach policy {} from group {}".format(
                            policy["PolicyArn"].split("/")[1], group["GroupName"]
                        )
                    )
                    self.iam_client.detach_group_policy(
                        GroupName=group["GroupName"], PolicyArn=policy["PolicyArn"]
                    )

        except Exception as e:
            logger.warning(str(e))
            pass

    def detach_role_policies(self):
        try:
            for role in self.iam_client.list_roles()["Roles"]:
                for policy in self.iam_client.list_attached_role_policies(
                    RoleName=role["RoleName"]
                )["AttachedPolicies"]:
                    logger.debug(
                        "Detach policy {} from role {}".format(
                            policy["PolicyArn"].split("/")[1], role["RoleName"]
                        )
                    )
                    self.iam_client.detach_role_policy(
                        RoleName=role["RoleName"], PolicyArn=policy["PolicyArn"]
                    )

        except Exception as e:
            logger.warning(str(e))
            pass

    def delete_policy_versions(self):
        try:
            for policy in self.iam_client.list_policies()["Policies"]:
                if ":policy/scality-internal/" in policy.get("Arn"):
                    continue
                for version in self.iam_client.list_policy_versions(
                    PolicyArn=policy["Arn"]
                )["Versions"]:
                    if version["IsDefaultVersion"] is False:
                        logger.debug(
                            "Delete policy version {}".format(
                                policy["Arn"].split("/")[1]
                            )
                        )
                        self.iam_client.delete_policy_version(
                            PolicyArn=policy["Arn"], VersionId=version["VersionId"]
                        )
        except Exception as e:
            logger.warning(str(e))
            pass

    def delete_policies(self):
        try:
            for policy in self.iam_client.list_policies()["Policies"]:
                if ":policy/scality-internal/" in policy.get("Arn"):
                    continue
                logger.debug("Delete policy {}".format(policy["Arn"].split("/")[1]))
                self.iam_client.delete_policy(PolicyArn=policy["Arn"])
        except Exception as e:
            logger.warning(str(e))
            pass

    def delete_roles(self):
        try:
            for role in self.iam_client.list_roles()["Roles"]:
                if role["RoleName"] == "service-backbeat-lifecycle-1":
                    continue
                logger.debug("Delete role {}".format(role["RoleName"]))
                self.iam_client.delete_role(RoleName=role["RoleName"])
        except Exception as e:
            logger.warning(str(e))
            pass

    def delete_groups(self):
        try:
            for group in self.iam_client.list_groups()["Groups"]:
                logger.debug("Delete group {}".format(group["GroupName"]))
                self.iam_client.delete_group(GroupName=group["GroupName"])
        except Exception as e:
            logger.warning(str(e))
            pass

    def delete_users(self):
        try:
            for user in self.iam_client.list_users()["Users"]:
                logger.debug("Delete user {}".format(user["UserName"]))
                self.iam_client.delete_user(UserName=user["UserName"])
        except Exception as e:
            logger.warning(str(e))
            pass
