import logging
import time
from functools import wraps
from typing import Callable, Dict, List

import boto3  # type: ignore
import scality.exceptions  # type: ignore
from fastapi import (  # type: ignore
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    Response,
    status,
)
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import <PERSON><PERSON><PERSON>In, FPUser, validate_user  # type: ignore
from jnapi_async import Jobs, Session, Settings  # type: ignore
from jnapi_async import exceptions as JNAPI_exceptions  # type: ignore
from jnapi_async.Module import ResponseMessage  # type: ignore
from scality.vault import VaultClient  # type: ignore

# from .. import oauth2_faker
from ..config import Config, Regions
from ..exceptions import AccessDenied, NoSuchEntity
from ..models import JobId, JsoBucketsCleanupInfo, QuotaResponse, VaultAccountResponse
from ..queues_names import JSO_ACCOUNT_CLEANUP_QUEUE
from ..s3_iam import Iam
from ..utils import Cache
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/vault", redirect_slashes=False, tags=["vault"])

settings = Settings()  # type: ignore
session = Session(
    gateway_api_url=settings.GATEWAY_API_URL,
    client_id=settings.CLIENT_ID,
    client_secret=settings.CLIENT_SECRET,
)

cache = Cache()


def handle_vault_errors(func: Callable):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except AccessDenied as e:
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_403_FORBIDDEN,
            )
        except NoSuchEntity as e:
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            logger.exception(f"Operation failed: {str(e)}")
            return JSONResponse(
                content=ResponseMessage(str(e)).dict(),
                status_code=status.HTTP_400_BAD_REQUEST,
            )

    return wrapper


async def get_vault_client(
    region: Regions = Query(..., title="S3 Region"),
) -> VaultClient:
    try:
        vault_host = config[region]["vault_host"]
        vault_port = config[region]["vault_port"]
        vault_access_key = config[region]["vault_access_key"]
        vault_secret_key = config[region]["vault_secret_key"]

        return VaultClient(
            host=vault_host,
            port=int(vault_port),
            use_https=True,
            access_key=vault_access_key,
            secret_access_key=vault_secret_key,
        )
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


@router.get(
    "/account/{account_name}",
    summary="GetVaultAccount",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        200: {"model": List[VaultAccountResponse]},
    },
    status_code=200,
)
@handle_vault_errors
async def GetVaultAccount(
    region: Regions = Query(..., title="S3 Region"),
    starts_with: bool = Query(default=False, title="Use account_name as a prefix"),
    ends_with: bool = Query(default=False, title="Use account_name as a suffix"),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
):
    accounts_list = []
    key = {"account_name": account_name, "region": region}

    if not starts_with and not ends_with:
        if cache.hit(key):
            logger.debug(f"Cache hit for {key}")
            accounts_list.append(cache.get(key))
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=accounts_list,
            )
        else:
            logger.debug(f"Cache miss for {key}")

    vault = await get_vault_client(region=region)

    marker = ""
    start_time = time.time()
    count = 0
    while True:
        resp = vault.list_accounts(marker=marker)
        accounts = resp["accounts"]
        count += len(accounts)

        if not starts_with and not ends_with:
            # Direct match search
            account = next(
                (account for account in accounts if account["name"] == account_name),
                None,
            )  # type: ignore

            if account:
                cache.set(key, account)
                accounts_list.append(account)
                break

        if starts_with or ends_with:
            # Filter based search
            filter_conditions = {
                (True, True): lambda x: x["name"].startswith(account_name)
                and x["name"].endswith(account_name),
                (True, False): lambda x: x["name"].startswith(account_name),
                (False, True): lambda x: x["name"].endswith(account_name),
            }
            filter_func = filter_conditions.get((starts_with, ends_with))
            if filter_func:
                filtered_accounts = filter(filter_func, accounts)
                accounts_list.extend(list(filtered_accounts))

        # Handle pagination and completion
        if not resp["isTruncated"]:
            if len(accounts_list) == 0:
                raise NoSuchEntity("Missing account")
            break
        marker = resp["marker"]  # paginates

    current_time = time.time()
    logger.debug(f"Processed {count} accounts in {str(current_time - start_time)} secs")

    return JSONResponse(status_code=status.HTTP_200_OK, content=accounts_list)


@router.delete(
    "/account/{account_name}",
    summary="DeleteVaultAccount",
    responses={
        500: {"model": Dict},
        422: {"model": Dict},
        404: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
async def DeleteVaultAccount(
    remove_buckets: bool = Query(default=False, title="Force empty bucket removal"),
    region: Regions = Query(..., title="S3 Region"),
    account_name: str = Path(..., title="Account name to delete"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    try:
        vault_host = config[region]["vault_host"]
        vault_port = config[region]["vault_port"]
        vault_access_key = config[region]["vault_access_key"]
        vault_secret_key = config[region]["vault_secret_key"]

        vault = VaultClient(
            host=vault_host,
            port=int(vault_port),
            use_https=True,
            access_key=vault_access_key,
            secret_access_key=vault_secret_key,
        )
    except Exception:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage("Region metadata not found").dict(),
        )

    try:
        root_keys = vault.generate_account_access_key(account_name)

        # check if any bucket exists
        s3_client = boto3.client(
            service_name="s3",
            endpoint_url=config[region]["s3_endpoint_url"],
            aws_access_key_id=root_keys["accessKey"],
            aws_secret_access_key=root_keys["secretKeyValue"],
            region_name=region,
            verify=False,
        )

        response = s3_client.list_buckets()
        if len(response["Buckets"]) > 0:
            if remove_buckets is False:
                return JSONResponse(
                    status_code=status.HTTP_409_CONFLICT,
                    content=ResponseMessage(
                        "Buckets exist under this account, please remove the buckets first"
                    ).dict(),
                )
            else:
                for bucket in response["Buckets"]:
                    s3_client.delete_bucket(Bucket=bucket["Name"])

        # account cannot be deleted if it is not empty
        iam = Iam(
            endpoint_url="https://{}:{}".format(vault_host, vault_port),
            region=region,
            access_key=root_keys["accessKey"],
            secret_access_key=root_keys["secretKeyValue"],
        )
        iam.remove_users_from_groups()
        iam.detach_role_policies()
        iam.detach_group_policies()
        iam.detach_user_policies()
        iam.delete_policy_versions()
        iam.delete_policies()
        iam.delete_roles()
        iam.delete_groups()
        iam.delete_users()

        # depending entities should be gone, delete the account now
        vault.delete_account(
            name=account_name,
        )
    except (scality.exceptions.NoSuchEntity, NoSuchEntity) as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )

    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.post(
    "/account/{account_name}/buckets/delete",
    summary="AccountBucketsCleanup",
    responses={500: {"model": Dict}, 422: {"model": Dict}, 201: {"model": JobId}},
    status_code=201,
)
async def AccountBucketsCleanup(
    region: Regions = Query(..., title="S3 Region"),
    dry_run: bool = Query(default=False, title="Dry run"),
    account_name: str = Path(..., title="The reference of the account to cleanup"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    """
    Empty and remove all buckets from an account (Async job)
    """

    service_id = f"jso_account_{account_name}"

    try:
        config[region]["s3_endpoint_url"]
    except Exception:
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage("Unknown region").dict(),
        )

    job = Jobs(session=session)
    try:
        job_id = await job.create(
            queue=JSO_ACCOUNT_CLEANUP_QUEUE,
            service_id=service_id,
            service_type="JSO",
            data={
                "region": region,
                "account": account_name,
                "dry_run": dry_run,
            },
        )
    except JNAPI_exceptions.ServiceLockedError:
        logger.exception(f"Service {service_id}: locked")
        return JSONResponse(
            status_code=status.HTTP_423_LOCKED,
            content=ResponseMessage(f"Service {service_id}: locked").dict(),
        )
    except Exception as e:
        logger.exception("Failed to create AccountBucketsCleanup job")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(str(e)).dict(),
        )

    # Return the job_id as a JSON response.
    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=JobId(job_id).dict()
    )


@router.get(
    "/account/{account_name}/buckets/delete",
    summary="AccountBucketsCleanupInfo",
    responses={**responses, 200: {"model": JsoBucketsCleanupInfo}},
    status_code=200,
)
async def AccountBucketsCleanupInfo(
    job_id: int,
    account_name: str = Path(..., title="The reference of the account to request"),
):
    """
    Get empty account status
    """

    if job_id <= 0:
        return JSONResponse(
            status_code=404,
            content=ResponseMessage(str("Invalid job id")).dict(),
        )

    try:
        jobs = Jobs(session=session)
        job = await jobs.get_job(job_id)
        if job.queue == JSO_ACCOUNT_CLEANUP_QUEUE:
            assert account_name == job.body["account"]

            if job.logs == []:
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content=JsoBucketsCleanupInfo(
                        job.service_id,
                        account_name,
                        job.state.value,
                        {},  # type: ignore
                    ).dict(),
                )
    except Exception as e:
        logger.exception(f"Failed to get cleanup job: {job_id}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get job {job_id}: {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=JsoBucketsCleanupInfo(
            job.service_id,
            account_name,
            job.state.value,
            job.logs[-1],  # type: ignore
        ).dict(),
    )


@router.get(
    "/account/{account_name}/quota",
    summary="GetVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        200: {"model": QuotaResponse},
    },
    status_code=200,
)
@handle_vault_errors
async def GetVaultAccountQuota(
    vault: VaultClient = Depends(get_vault_client),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
):
    resp = vault.get_account_quota(name=account_name)

    return QuotaResponse(**resp)


@router.put(
    "/account/{account_name}/quota",
    summary="UpdateVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
@handle_vault_errors
async def UpdateVaultAccountQuota(
    vault: VaultClient = Depends(get_vault_client),
    account_name: str = Path(..., title="Account name"),
    quota_bytes: int = Query(..., title="Quota in bytes"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    vault.update_account_quota(name=account_name, quota=quota_bytes)

    return Response(status_code=status.HTTP_204_NO_CONTENT)


@router.delete(
    "/account/{account_name}/quota",
    summary="DeleteVaultAccountQuota",
    responses={
        422: {"model": Dict},
        404: {"model": Dict},
        403: {"model": Dict},
        400: {"model": Dict},
        204: {"model": None},
    },
    status_code=204,
)
@handle_vault_errors
async def DeleteVaultAccountQuota(
    vault: VaultClient = Depends(get_vault_client),
    account_name: str = Path(..., title="Account name"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
):
    vault.delete_account_quota(name=account_name)

    return Response(status_code=status.HTTP_204_NO_CONTENT)
