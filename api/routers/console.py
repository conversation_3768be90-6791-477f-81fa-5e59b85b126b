import logging
import secrets

from fastapi import (  # type: ignore
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    status,
)
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import AnyRoleIn, FPUser, validate_user  # type: ignore
from jnapi_async.Module import ResponseMessage  # type: ignore

# from .. import oauth2_faker
from ..config import Config, Regions
from ..exceptions import AccessDenied, EntityAlreadyExists, NoSuchEntity
from ..models import (
    AccountInstanceInputs,
    AccountInstanceResponse,
    ConsoleAccountResponse,
)
from ..providers import ProviderFactory
from ..utils import Cache
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/console", redirect_slashes=False, tags=["console"])

cache = Cache()


async def get_console_provider(
    region: Regions = Query(..., title="S3 Region"),
):
    """Get console provider for the specified region"""
    try:
        return ProviderFactory.get_console_provider(region)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


async def get_account_provider(
    region: Regions = Query(..., title="S3 Region"),
):
    """Get account provider for the specified region"""
    try:
        return ProviderFactory.get_account_provider(region)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Region metadata not found"
        )


@router.get(
    "/account/{account_name}",
    summary="GetConsoleAccount",
    responses={**responses, 200: {"model": ConsoleAccountResponse}},
    status_code=200,
)
async def GetConsoleAccount(
    console_provider=Depends(get_console_provider),
    account_name: str = Path(..., title="Account name"),
    region: Regions = Query(..., title="S3 Region"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_read"))),
    # fake_auth=Depends(oauth2_faker),
):
    key = {"account_name": account_name, "region": region}
    if cache.hit(key):
        return JSONResponse(status_code=status.HTTP_200_OK, content=cache.get(key))

    try:
        account = await console_provider.get_account(account_name)
        cache.set(key, account)
    except AccessDenied as e:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except NoSuchEntity as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to get account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to get account '{account_name}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(status_code=status.HTTP_200_OK, content=account)


@router.post(
    "/account",
    summary="CreateConsoleAccount",
    responses={**responses, 201: {"model": dict}},
    status_code=201,
    response_model=AccountInstanceResponse,
)
async def CreateConsoleAccount(
    instance: AccountInstanceInputs,
    region: Regions = Query(..., title="S3 Region"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
    # fake_auth=Depends(oauth2_faker),
):
    data = instance.dict()

    # Get providers
    console_provider = ProviderFactory.get_console_provider(region)
    account_provider = ProviderFactory.get_account_provider(region)

    account_password = secrets.token_urlsafe(16)
    account_name = data["accountName"]
    account_console = data["console"]
    account_quota = data["quota"]
    account_email = account_name + "@freepro.com"

    endpointUrl = config[region]["s3_endpoint_url"]
    accountId = None
    accountName = account_name
    emailAddress = account_email
    consolePassword = None
    accessKey = None
    secretAccessKey = None

    try:
        if account_console is True:
            await console_client.authenticate(
                console_username=console_username,
                console_password=console_password,
            )
            resp = await console_client.create_account(
                account_name=account_name,
                email=account_email,
                quota=account_quota,
                password=account_password,
            )
            accountId = str(resp["account"]["id"])
            consolePassword = account_password
        else:
            resp = vault_client.create_account(
                name=account_name, email=account_email, quota=account_quota
            )
            accountId = str(resp["arn"].split(":")[4])
            consolePassword = ""

        # due to a bug in with the Console API, always regenerate superadmin access keys
        root_user_keys = vault_client.generate_account_access_key(account_name)
        accessKey = root_user_keys.get("accessKey")
        secretAccessKey = root_user_keys.get("secretKeyValue")

        account_info = AccountInstanceResponse(
            endpointUrl=endpointUrl,
            accountId=accountId,
            accountName=accountName,
            emailAddress=emailAddress,
            consolePassword=consolePassword,
            accessKey=accessKey,  # type: ignore
            secretAccessKey=secretAccessKey,  # type: ignore
        )
    except AccessDenied as e:
        logging.error(str(e))
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except EntityAlreadyExists as e:
        return JSONResponse(
            status_code=status.HTTP_409_CONFLICT,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to create new account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to create new account '{account_name}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=account_info.dict()
    )


@router.delete(
    "/account/{account_name}",
    summary="DeleteConsoleAccount",
    responses={**responses, 204: {"model": None}},
    status_code=204,
)
async def DeleteConsoleAccount(
    remove_buckets: bool = Query(default=False, title="Force empty bucket removal"),
    region: Regions = Query(..., title="S3 Region"),
    account_name: str = Path(..., title="Account name to delete"),
    _: FPUser = Depends(validate_user(AnyRoleIn("jso_admin_api_write"))),
    # fake_auth=Depends(oauth2_faker),
):
    try:
        url = config[region]["s3_endpoint_url"]
        console_username = config[region]["console_username"]
        console_password = config[region]["console_password"]
        vault_host = config[region]["vault_host"]
        vault_port = config[region]["vault_port"]
        vault_access_key = config[region]["vault_access_key"]
        vault_secret_key = config[region]["vault_secret_key"]
    except Exception:
        return JSONResponse(
            status_code=500, content=ResponseMessage("Unknown region").dict()
        )

    try:
        vault = VaultClient(
            host=vault_host,
            port=int(vault_port),
            use_https=True,
            access_key=vault_access_key,
            secret_access_key=vault_secret_key,
        )
        root_keys = vault.generate_account_access_key(account_name)
        logger.debug(root_keys)

        # check if any bucket exists
        s3_client = boto3.client(
            service_name="s3",
            endpoint_url=config[region]["s3_endpoint_url"],
            aws_access_key_id=root_keys["accessKey"],
            aws_secret_access_key=root_keys["secretKeyValue"],
            region_name=region,
            verify=False,
        )

        response = s3_client.list_buckets()
        if len(response["Buckets"]) > 0:
            if remove_buckets is False:
                return JSONResponse(
                    status_code=status.HTTP_409_CONFLICT,
                    content=ResponseMessage(
                        "Buckets exist under this account, please remove the buckets first"
                    ).dict(),
                )
            else:
                for bucket in response["Buckets"]:
                    s3_client.delete_bucket(Bucket=bucket["Name"])

        # account cannot be deleted if it is not empty
        iam = Iam(
            endpoint_url="https://{}:{}".format(vault_host, vault_port),
            region=region,
            access_key=root_keys["accessKey"],
            secret_access_key=root_keys["secretKeyValue"],
        )
        iam.remove_users_from_groups()
        iam.detach_role_policies()
        iam.detach_group_policies()
        iam.detach_user_policies()
        iam.delete_policy_versions()
        iam.delete_policies()
        iam.delete_roles()
        iam.delete_groups()
        iam.delete_users()
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )

    console = Console(url)
    await console.authenticate(
        console_username=console_username,
        console_password=console_password,
    )

    try:
        await console.delete_account(
            account_name=account_name,
        )

        await console.delete_account_user(
            account_name=account_name,
        )

    except AccessDenied as e:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=ResponseMessage(str(e)).dict(),
        )
    except NoSuchEntity as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(str(e)).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to delete account '{account_name}'")
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(f"Failed to delete account '{account_name}': {str(e)}")
            ).dict(),
        )
