import logging

from fastapi import APIRouter, Path, Query, status  # type: ignore
from fastapi.responses import JSONResponse  # type: ignore
from jnapi_async.Module import ResponseMessage  # type: ignore
from typing_extensions import Annotated

from ..config import Config, Regions
from ..models import MetricsResponse
from ..s3_utapi import Instance, Utapi
from . import responses

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

config = Config().get_config_parser()
router = APIRouter(prefix="/metrics", redirect_slashes=False, tags=["metrics"])


@router.get(
    "/account/{account_id}",
    summary="AccountMetrics",
    responses={**responses, 200: {"model": MetricsResponse}},
    status_code=200,
)
async def AccountMetrics(
    human: bool = Query(default=False, title="Output in human readable format"),
    current: bool = Query(default=True, title="Last 15 minutes"),
    week: bool = Query(default=False, title="Last 7 days"),
    month: bool = Query(default=False, title="Last 30 days"),
    year: bool = Query(default=False, title="Last 365 days"),
    custom: bool = Query(
        default=False, title="Custom timeframe (invalidates others timeframe)"
    ),
    start_time: Annotated[
        int, Query(title="Custom start time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    end_time: Annotated[
        int, Query(title="Custom end time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    region: Regions = Query(..., title="S3 Region"),
    account_id: str = Path(..., title="ID of the account"),
):
    if not (current or week or month or year or custom):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(
                    "At least one timeframe must be selected (current, week, month, year, custom)"
                )
            ).dict(),
        )

    selector = []
    custom_time = {}
    if current:
        selector.append("current")
    if week:
        selector.append("week")
    if month:
        selector.append("month")
    if year:
        selector.append("year")
    if custom:
        selector = ["custom"]
        custom_time = {"start_time": start_time, "end_time": end_time}

    try:
        u = Utapi(region=region)
        res = await u.get_metrics(
            Instance.ACCOUNT, account_id, human, selector, custom_time
        )
    except UnboundLocalError:
        logger.exception(f"Failed to get metrics for account '{account_id}'")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(
                    f"Failed to get metrics for account '{account_id}': account not found"
                )
            ).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to get metrics for account '{account_id}'")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get metrics for account '{account_id}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=res,
    )


@router.get(
    "/user/{user}",
    summary="UserMetrics",
    responses={**responses, 200: {"model": MetricsResponse}},
    status_code=200,
)
async def UserMetrics(
    human: bool = Query(default=False, title="Output in human readable format"),
    current: bool = Query(default=True, title="Last hour"),
    week: bool = Query(default=False, title="Last week"),
    month: bool = Query(default=False, title="Last month"),
    year: bool = Query(default=False, title="Last year"),
    custom: bool = Query(
        default=False, title="Custom timeframe (invalidates others timeframe)"
    ),
    start_time: Annotated[
        int, Query(title="Custom start time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    end_time: Annotated[
        int, Query(title="Custom end time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    region: Regions = Query(..., title="S3 Region"),
    user: str = Path(..., title="The reference of the username to request"),
):
    if not (current or week or month or year or custom):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(
                    "At least one timeframe must be selected (current, week, month, year, custom)"
                )
            ).dict(),
        )

    selector = []
    custom_time = {}
    if current:
        selector.append("current")
    if week:
        selector.append("week")
    if month:
        selector.append("month")
    if year:
        selector.append("year")
    if custom:
        selector = ["custom"]
        custom_time = {"start_time": start_time, "end_time": end_time}

    try:
        u = Utapi(region=region)
        res = await u.get_metrics(Instance.USER, user, human, selector, custom_time)
    except UnboundLocalError:
        logger.exception(f"Failed to get metrics for user '{user}'")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get metrics for user '{user}': user not found")
            ).dict(),
        )
    except Exception as e:
        logger.exception(f"Failed to get metrics for user '{user}'")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get metrics for user '{user}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=res,
    )


@router.get(
    "/bucket/{bucket}",
    summary="BucketMetrics",
    responses={**responses, 200: {"model": MetricsResponse}},
    status_code=200,
)
async def BucketMetrics(
    human: bool = Query(default=False, title="Output in human readable format"),
    current: bool = Query(default=True, title="Last hour"),
    week: bool = Query(default=False, title="Last week"),
    month: bool = Query(default=False, title="Last month"),
    year: bool = Query(default=False, title="Last year"),
    custom: bool = Query(
        default=False, title="Custom timeframe (invalidates others timeframe)"
    ),
    start_time: Annotated[
        int, Query(title="Custom start time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    end_time: Annotated[
        int, Query(title="Custom end time UTC POSIX Timestamp (seconds)", ge=0)
    ] = 0,
    region: Regions = Query(..., title="S3 Region"),
    bucket: str = Path(..., title="The reference of the bucket to request"),
):
    if not (current or week or month or year or custom):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=ResponseMessage(
                str(
                    "At least one timeframe must be selected (current, week, month, year, custom)"
                )
            ).dict(),
        )

    selector = []
    custom_time = {}
    if current:
        selector.append("current")
    if week:
        selector.append("week")
    if month:
        selector.append("month")
    if year:
        selector.append("year")
    if custom:
        selector = ["custom"]
        custom_time = {"start_time": start_time, "end_time": end_time}

    try:
        u = Utapi(region=region)
        res = await u.get_metrics(Instance.BUCKET, bucket, human, selector, custom_time)
    except UnboundLocalError:
        logger.exception(f"Failed to get metrics for bucket '{bucket}'")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get metrics for bucket '{bucket}': bucket not found")
            ).dict(),
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get metrics for bucket '{bucket}': {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=res,
    )
