import json
import logging
from contextlib import asynccontextmanager
from os import getenv

import sentry_sdk  # type: ignore
from fastapi import <PERSON><PERSON><PERSON>, Path, Request, status  # type: ignore
from fastapi.encoders import jsonable_encoder  # type: ignore
from fastapi.exceptions import RequestValidationError  # type: ignore
from fastapi.responses import JSONResponse  # type: ignore
from fp_aaa_kit import KEY_MANAGER  # type: ignore
from fp_aaa_kit.openapi import add_identity_providers_in_openapi_schema  # type: ignore
from jnapi_async import Jobs, Session, Settings  # type: ignore
from jnapi_async import exceptions as JNAPI_exceptions  # type: ignore
from jnapi_async.Module import ResponseMessage  # type: ignore
from sentry_sdk.integrations.fastapi import FastApiIntegration  # type: ignore
from sentry_sdk.integrations.logging import (  # type: ignore
    LoggingIntegration,
    ignore_logger,
)
from sentry_sdk.integrations.starlette import StarletteIntegration  # type: ignore

from .config import Config
from .models import (
    BackInTimeInstance,
    JobId,
    JsoObjectsCleanupInfo,
    JsoObjectsCleanupInstance,
    ServiceId,
)
from .queues_names import JSO_BACKINTIME_QUEUE, JSO_OBJECTS_CLEANUP_QUEUE
from .routers import console, metrics, vault
from .utils import application_settings

responses = {
    401: {"model": ResponseMessage},
    404: {"model": ResponseMessage},
    403: {"model": ResponseMessage},
    409: {"model": ResponseMessage},
    423: {"model": ResponseMessage},
    500: {"model": ResponseMessage},
}

config = Config().get_config_parser()

settings = Settings()  # type: ignore
session = Session(
    gateway_api_url=settings.GATEWAY_API_URL,
    identity_provider_url=settings.IDENTITY_PROVIDER_URL,
    realm=settings.IDENTITY_PROVIDER_REALM,
    client_id=settings.CLIENT_ID,
    client_secret=settings.CLIENT_SECRET,
)

logging.basicConfig(format="{levelname:7} {message}", style="{", level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

__sentry_project = getenv("SENTRY_PROJECT")
__sentry_dsn = getenv("SENTRY_DSN")
__version = getenv("VERSION", "dev")
__environment = getenv("ENVIRONMENT", "poc")
if __sentry_project is not None and __sentry_dsn is not None:
    sentry_sdk.init(
        dsn=__sentry_dsn,
        traces_sample_rate=1.0,
        release=f"{__sentry_project}@{__version}",
        environment=__environment,
        enable_tracing=True,
        sample_rate=1.0,
        profiles_sample_rate=1.0,
        shutdown_timeout=10,
        integrations=[
            StarletteIntegration(transaction_style="endpoint"),
            FastApiIntegration(transaction_style="endpoint"),
            LoggingIntegration(
                level=logging.INFO,  # Capture info and above as breadcrumbs
                event_level=logging.WARNING,  # Send warnings as events
            ),
        ],
    )
    # Ignore jnapi_async to avoid sentry issues
    ignore_logger("jnapi_async")

    sentry_sdk.set_tag("service", "api")


@asynccontextmanager
async def app_lifespan(main_router: FastAPI):
    try:
        idp_root_urls = json.loads(application_settings.IDP_ROOT_URI)
    except json.decoder.JSONDecodeError:
        idp_root_urls = application_settings.IDP_ROOT_URI

    try:
        await KEY_MANAGER.set_up_endpoint(identity_provider_root_urls=idp_root_urls)
        add_identity_providers_in_openapi_schema(main_router)
    except Exception as e:
        logger.exception("Failed to set up identity provider key manager")
        raise e

    # Initiate a session here so the session will automatically be renewed when it expires.
    try:
        await session._fetch_token()
    except Exception as e:
        logger.exception(
            f"Failed to initiate a session with {session.identity_provider_url}"
        )
        raise e

    yield  # Give the hand to the main router
    await KEY_MANAGER.clear_auto_refresh()
    sentry_sdk.flush()


def include_routers(api: FastAPI) -> None:
    api.include_router(metrics.router)
    api.include_router(vault.router)
    api.include_router(console.router)


api = FastAPI(root_path="/jso", lifespan=app_lifespan)
include_routers(api)


@api.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    logger.error(jsonable_encoder({"detail": exc.errors(), "body": exc.body}))  # type: ignore

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": exc.errors(), "body": exc.body}),  # type: ignore
    )


@api.on_event("startup")
async def startup():
    # Initiate a session here so the session will automatically be renewed when it expires.
    await session._fetch_token()


@api.get("/healthcheck", summary="healthcheck", response_model=ResponseMessage)
async def healthcheck():
    return JSONResponse(
        status_code=status.HTTP_200_OK, content=ResponseMessage("Healthy").dict()
    )


@api.post(
    "/{service_id}/backintime",
    summary="BackInTime",
    responses={**responses, 200: {"model": JobId}},
    status_code=200,
)
async def BackInTime(
    instance: BackInTimeInstance,
    service_id: ServiceId = Path(  # type: ignore
        ..., title="The reference of the instance to work on"
    ),
):
    """
    Build indexes for TimeMachine like feature (Async job)
    """

    data = instance.dict()
    data["date"] = data["date"].isoformat()

    job = Jobs(session=session)
    try:
        job_id = await job.create(
            queue=JSO_BACKINTIME_QUEUE,
            service_id=service_id,
            data=data,
        )
    except JNAPI_exceptions.ServiceLockedError:
        logger.exception(f"Service {service_id}: locked")
        return JSONResponse(
            status_code=status.HTTP_423_LOCKED,
            content=ResponseMessage(f"Service {service_id}: locked").dict(),
        )
    except Exception as e:
        logger.exception("Failed to create job")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(str(e)).dict(),
        )

    # Return the job_id as a JSON response.
    return JSONResponse(status_code=status.HTTP_200_OK, content=JobId(job_id).dict())


@api.post(
    "/bucket/{bucket}/objects/delete",
    summary="ObjectsCleanup",
    responses={
        500: {"model": dict},
        423: {"model": dict},
        404: {"model": dict},
        201: {"model": JobId},
    },
    status_code=201,
)
async def ObjectsCleanup(
    instance: JsoObjectsCleanupInstance,
    bucket: str = Path(..., title="The reference of the bucket to cleanup"),
):
    """
    Empty a bucket (Async job)
    """

    data = instance.dict()
    data["bucket"] = bucket
    service_id = "jso_" + bucket
    try:
        data["endpoint_url"] = config[data["region"]]["s3_endpoint_url"]
    except Exception:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage("Unknown region").dict(),
        )

    job = Jobs(session=session)
    try:
        job_id = await job.create(
            queue=JSO_OBJECTS_CLEANUP_QUEUE,
            service_id=service_id,
            data=data,
        )
    except JNAPI_exceptions.ServiceLockedError:
        logger.exception(f"Service {service_id}: locked")
        return JSONResponse(
            status_code=status.HTTP_423_LOCKED,
            content=ResponseMessage(f"Service {service_id}: locked").dict(),
        )
    except Exception as e:
        logger.exception("Failed to create job")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ResponseMessage(str(e)).dict(),
        )

    # Return the job_id as a JSON response.
    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content=JobId(job_id).dict()
    )


@api.get(
    "/bucket/{bucket}/objects/delete",
    summary="ObjectsCleanupInfo",
    responses={**responses, 200: {"model": JsoObjectsCleanupInfo}},
    status_code=200,
)
async def ObjectsCleanupInfo(
    job_id: int,
    bucket: str = Path(..., title="The reference of the bucket to request"),
):
    """
    Get empty bucket status
    """

    if job_id <= 0:
        return JSONResponse(
            status_code=404,
            content=ResponseMessage(str("Invalid job id")).dict(),
        )

    try:
        jobs = Jobs(session=session)
        job = await jobs.get_job(job_id)

        if job.queue == JSO_OBJECTS_CLEANUP_QUEUE:
            assert bucket == job.body["bucket"]

            if job.logs == []:
                return JSONResponse(
                    status_code=status.HTTP_200_OK,
                    content=JsoObjectsCleanupInfo(
                        job.service_id,
                        bucket,
                        job.state.value,
                        {},  # type: ignore
                    ).dict(),
                )
    except Exception as e:
        logger.exception(f"Failed to get cleanup job: {job_id}")
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ResponseMessage(
                str(f"Failed to get job {job_id}: {str(e)}")
            ).dict(),
        )

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=JsoObjectsCleanupInfo(
            job.service_id,
            bucket,
            job.state.value,
            job.logs[-1],  # type: ignore
        ).dict(),
    )
