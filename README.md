# JSO

Module for JSO operations

## Multi-Regions

The module handles multi-regions using credentials stored in CI/CD File Variables (CONFIG_FILE) which are integrated into the Docker container at build time.
When adding a new region,
- Update CONFIG_FILE
- Add region name to the Regions class in jso_module/config.py

## Run in development

* Install dependencies 
```
pip3 install just-bin
pip3 install uv
uv sync
```

* Start bootstrap dev
from bootstrap-dev-environment directory
```
cd bootstrap-dev-environment
make up
```

* Start uvicorn in development mode
```
source tests/set_env.sh
just start_server
```

* Check if apps is running
```
curl http://localhost:8071/healthcheck

```
* Start the runners in development mode
```
source tests/set_env.sh
just start_runners
```

* Run Tests
```
source tests/set_env.sh
just tests
```

* Build and Run container locally
```
touch .config
source tests/set_env.sh
just docker_build

just docker_run
(it should start but fail because it cannot connect to api gw)
```