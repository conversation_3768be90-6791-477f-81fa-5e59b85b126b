# JSO

Module for JSO operations with multi-provider S3 support

## Architecture

JSO now supports multiple S3 providers through a provider abstraction layer while maintaining backward compatibility with all existing API endpoints.

### Provider Abstraction Layer
- **Multi-provider support**: Easily switch between different S3 providers (Scality, AWS, etc.)
- **Factory pattern**: Centralized provider instantiation with singleton caching
- **Abstract interfaces**: 5 base provider classes for different operations
- **Backward compatibility**: All existing API endpoints unchanged

### Supported Providers
- **Scality** (current default): Full implementation using VaultClient, Console, Utapi
- **Extensible**: Framework ready for AWS, MinIO, or other S3-compatible providers

## Multi-Regions

The module handles multi-regions using credentials stored in CI/CD File Variables (CONFIG_FILE) which are integrated into the Docker container at build time.

### Configuration Structure
The configuration uses INI format with region-specific sections. The system uses Python's `configparser` to load the configuration from `api/.config`. See `api/.config.example`:

```ini
[fr-lab]
provider = scality
console_username = XXXXXXXXXXXXXXXXXXXX
console_password = XXXXXXXXXXXXXXXXXXXX
utapi_host = s3.fr-lab.jaguar-network.com
utapi_port = 8100
utapi_access_key = XXXXXXXXXXXXXXXXXXXX
utapi_secret_key = XXXXXXXXXXXXXXXXXXXX
vault_host = s3.fr-lab.jaguar-network.com
vault_port = 8600
vault_access_key = XXXXXXXXXXXXXXXXXXXX
vault_secret_key = XXXXXXXXXXXXXXXXXXXX
s3_endpoint_url = https://s3.fr-lab.jaguar-network.com
```

**Configuration Loading:**
- The `Config` class in `api/config.py` uses `configparser.ConfigParser()` to read the INI file
- Each region section contains provider-specific configuration parameters
- The ProviderFactory reads the `provider` key to determine which provider implementation to use
- All other keys in the section are passed as configuration parameters to the provider

When adding a new region:
- Add new region section to CONFIG_FILE with provider-specific configuration
- Add region name to the Regions class in `api/config.py`
- Provider factory automatically handles region-specific provider instances

## Run in development

* Install dependencies 
```
pip3 install just-bin
pip3 install uv
uv sync
```

* Start bootstrap dev
from bootstrap-dev-environment directory
```
cd bootstrap-dev-environment
make up
```

* Start uvicorn in development mode
```
source set_env.sh
just start_server
```

* Check if apps is running
```
curl http://localhost:8071/healthcheck

```
* Start the runners in development mode
```
source set_env.sh
just start_runners
```

* Run Tests
```
source set_env.sh
just tests
```

## Testing

### Integration Tests
Run the full integration test suite:
```bash
source set_env.sh
just tests
```

### Provider Unit Tests
Test the provider abstraction layer:
```bash
# Run provider unit tests
just test-providers-pytest

# Alternative method using custom runner
just test-providers

# Run specific provider test class
just test-provider-class TestProviderFactory
```

### Provider Integration Validation
Validate that routers properly use the provider abstraction:
```bash
just validate-provider-integration
```

### Metrics Functional Tests
Comprehensive functional testing for all metrics endpoints:
```bash
# Run all metrics functional tests
just test-metrics

# Run specific metrics test method
just test-metrics-method test_account_metrics_current

# Enhanced test runner with environment validation
just test-metrics-runner

# List all available metrics test methods
just list-metrics-tests

# Validate metrics test suite structure
just validate-metrics-tests
```

**Environment Setup for Metrics Tests:**
```bash
# Set up test environment
source tests/set_env_metrics.sh

# Set required variables (example)
export CLIENT_ID_JSO="your-client-id"
export CLIENT_SECRET_JSO="your-client-secret"
export ACCOUNT_ID="test-account-id"
export TEST_USER="test-user"
export TEST_BUCKET="test-bucket"
```

### All Tests
Run both integration and provider tests:
```bash
just test-all
```

## Provider Architecture

### Adding New Providers

To add support for a new S3 provider:

1. **Create provider implementation**:
```python
# api/providers/aws_provider.py
class AWSAccountProvider(BaseAccountProvider):
    async def get_account(self, account_name: str) -> List[dict]:
        # AWS-specific implementation
```

2. **Update ProviderFactory**:
```python
# Add AWS provider instantiation logic
if provider_type == "aws":
    return AWSAccountProvider(region, config)
```

3. **Update configuration**:
```ini
[us-east-1]
provider = aws
# AWS-specific configuration
aws_access_key_id = XXXXXXXXXXXXXXXXXXXX
aws_secret_access_key = XXXXXXXXXXXXXXXXXXXX
aws_region = us-east-1
s3_endpoint_url = https://s3.amazonaws.com
```

### Provider Interfaces

The system defines 5 abstract provider interfaces:
- `BaseAccountProvider` - Account management operations
- `BaseMetricsProvider` - Metrics and monitoring
- `BaseConsoleProvider` - Console account operations
- `BaseS3Provider` - S3 client operations
- `BaseIAMProvider` - IAM operations

* Build and Run container locally
```
touch .config
source tests/set_env.sh
just docker_build

just docker_run
(it should start but fail because it cannot connect to api gw)
```

## Development Workflow

### Code Quality
```bash
# Lint and format code
just lint
```

### Testing Workflow
1. **Unit Tests**: Start with provider unit tests during development
2. **Integration Validation**: Verify router integration with validation script
3. **Integration Tests**: Run full integration tests before deployment

### File Structure
```
api/
├── providers/
│   ├── base.py              # Abstract provider interfaces
│   ├── factory.py           # Provider factory with caching
│   ├── scality_provider.py  # Scality implementation
│   └── __init__.py
├── routers/
│   ├── vault.py            # Account operations (uses ProviderFactory)
│   ├── console.py          # Console operations (uses ProviderFactory)
│   └── metrics.py          # Metrics operations (uses ProviderFactory)
└── config.py               # Region and configuration management

tests/
├── test_providers.py                    # Provider unit tests (21 tests)
├── validate_provider_integration.py    # Router integration validation
├── test_console.py                     # Integration tests
└── test_cleanup.py                     # Integration tests
```

## Migration Notes

The provider abstraction layer maintains **100% backward compatibility**:
- All existing API endpoints work unchanged
- No breaking changes to request/response formats
- Existing client code requires no modifications

